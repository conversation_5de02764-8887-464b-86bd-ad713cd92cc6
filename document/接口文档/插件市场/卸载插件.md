# 卸载插件

## 接口描述
根据插件名称卸载插件，全量删除该插件的所有规则。目前支持外部认证插件的卸载。

## 请求信息

### 请求路径
```
DELETE /api/aigw/v1/aigateway/{instanceId}/pluginMarket/{pluginName}
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | string | 是 | AI网关实例ID |
| pluginName | string | 是 | 插件名称，目前仅支持：ext-auth |

### 请求示例
```
DELETE /api/aigw/v1/aigateway/instance123/pluginMarket/ext-auth
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| status | integer | HTTP状态码 |
| message | string | 响应消息 |
| pluginName | string | 插件名称 |
| deletedCount | integer | 删除的规则数量 |
| uninstallTime | string | 卸载时间 |

### 响应示例

#### 成功响应
```json
{
  "success": true,
  "status": 200,
  "message": "插件卸载成功，共删除 5 个规则",
  "pluginName": "ext-auth",
  "deletedCount": 5,
  "uninstallTime": "2024-01-15 14:30:25"
}
```

#### 部分成功响应
```json
{
  "success": true,
  "status": 200,
  "message": "插件卸载部分成功，成功删除 3 个规则，失败 2 个",
  "pluginName": "ext-auth",
  "deletedCount": 3,
  "uninstallTime": "2024-01-15 14:30:25"
}
```

#### 无插件时响应
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "ResourceNotFoundException",
  "message": {
    "global": "未找到需要卸载的插件"
  }
}
```

## 错误码

### 通用错误码
- `MissingParametersException`: 缺少必要参数
- `InvalidParameterValueException`: 参数值无效
- `ResourceNotFoundException`: 未找到需要卸载的插件

### 错误响应示例
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "InvalidParameterValueException",
  "message": {
    "global": "不支持的插件名称，目前仅支持: ext-auth"
  }
}
```

## 使用说明

1. **支持的插件**: 目前仅支持外部认证插件（ext-auth）的卸载
2. **全量删除**: 卸载操作会删除该插件的所有规则，包括网关维度和路由维度的规则
3. **性能优化**: 使用并发删除机制，提高大量规则删除的性能
4. **部分成功**: 如果部分规则删除失败，接口仍会返回成功，但会在消息中说明失败情况
5. **幂等性**: 重复调用卸载接口不会产生错误，如果插件已不存在会返回相应错误

## 注意事项

1. **不可逆操作**: 卸载操作不可逆，删除的规则无法恢复
2. **影响范围**: 卸载会影响所有使用该插件的路由和网关配置
3. **权限要求**: 需要通过IAM签名验证
4. **性能考虑**: 大量规则的删除可能需要一定时间，接口会等待所有删除操作完成后返回

## 技术实现

### 性能优化特性
1. **批量查询**: 使用标签选择器一次性查询所有相关插件实例
2. **并发删除**: 使用goroutine并发删除多个插件实例
3. **结果收集**: 通过channel收集删除结果，确保所有操作完成
4. **错误统计**: 统计成功和失败的删除数量，提供详细的操作结果

### 删除流程
1. 验证插件名称和实例ID
2. 使用标签选择器查询所有相关的WasmPlugin资源
3. 并发删除所有找到的插件实例
4. 收集删除结果并统计成功/失败数量
5. 返回详细的卸载结果信息
