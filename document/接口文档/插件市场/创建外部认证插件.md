# 创建外部认证插件

## 接口描述
创建外部认证插件，支持网关维度和路由维度两种生效粒度。

## 请求信息

### 请求路径
```
POST /api/aigw/v1/aigateway/{instanceId}/extAuth
```

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| instanceId | string | 是 | AI网关实例ID |

#### 请求体参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| enabled | boolean | 是 | 是否启用插件 |
| name | string | 是 | 插件名称，长度2-64个字符 |
| description | string | 否 | 插件描述，最大200个字符 |
| scope | string | 否 | 生效粒度，可选值：gateway(网关维度)、route(路由维度)，默认gateway |
| matchType | string | 否 | 匹配类型，可选值：blacklist、whitelist，仅路由维度时必填，默认blacklist |
| routeList | array[string] | 否 | 路由名称列表，仅路由维度时必填 |
| httpService | string | 是 | HTTP服务配置（YAML格式字符串） |
| statusOnError | integer | 否 | 认证失败时的HTTP状态码，默认403，范围400-599 |

### 请求示例

#### 网关维度外部认证
```json
{
  "enabled": true,
  "name": "my-gateway-auth",
  "description": "网关级别认证",
  "scope": "gateway",
  "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /auth\ntimeout: 2000",
  "statusOnError": 403
}
```

#### 路由维度外部认证
```json
{
  "enabled": true,
  "name": "my-route-auth",
  "description": "路由级别认证",
  "scope": "route",
  "matchType": "blacklist",
  "routeList": ["api-route-v1", "user-route"],
  "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /validate\ntimeout: 2000",
  "statusOnError": 401
}
```

## 响应信息

### 响应参数
| 参数名 | 类型 | 描述 |
|--------|------|------|
| id | string | 外部认证插件ID |
| enabled | boolean | 是否启用 |
| name | string | 插件名称 |
| description | string | 插件描述 |
| scope | string | 生效粒度 |
| matchType | string | 匹配类型（仅路由维度） |
| routeList | array[string] | 路由名称列表（仅路由维度） |
| httpService | string | HTTP服务配置（YAML格式字符串） |
| statusOnError | integer | 认证失败状态码 |
| createTime | string | 创建时间 |

### 响应示例

#### 成功响应
```json
{
  "success": true,
  "status": 200,
  "result": {
    "id": "my-gateway-auth",
    "enabled": true,
    "name": "my-gateway-auth",
    "description": "网关级别认证",
    "scope": "gateway",
    "httpService": "endpoint_mode: envoy\nendpoint:\n  service_name: auth-service.default.svc.cluster.local\n  service_port: 8080\n  path_prefix: /auth\ntimeout: 2000",
    "statusOnError": 403,
    "createTime": "2024-01-15 10:30:00"
  }
}
```

#### 失败响应
```json
{
  "success": false,
  "requestId": "9eb940ce-bb3f--aigw-bcecanarytag",
  "code": "InvalidParameterValueException",
  "message": {
    "global": "One of the parameters in the request is invalid"
  }
}
```

## 错误码
| 错误码 | 描述 |
|--------|------|
| MissingParametersException | 缺少必要参数 |
| InvalidParameterValueException | 参数值无效 |
| ResourceConflictException | 资源已存在 |
| InternalServerError | 服务器内部错误 |
