app: csm

cce:
  iamProfile: "staging"

server:
  listen: 0.0.0.0
  port: 8303
  write_timeout: 60s
  pprof_enabled: true
  metric_enabled: true
  # 开启访问 request 日志
  access_log: true
  # true 表示在控制台打印启动参数，false 表示不打印
  print_flag: false
  logs:
    logDir: "logs"
    logLevel: "info"
    maxSize: 500 # 文件大小限制,单位MB 默认值 500MB
    maxBackups: 10 # 最大保留日志文件数量 默认值 10
    maxAge: 30 # 日志文件保留天数 默认值 30

region: "global"
serviceName: "bce:csm"
bceServiceRole: "BceServiceRole_csm"

# 注册中心配置
register:
  region: "gz"
  cluster: "cce-e3ca4ffj"
  release: "1.1.0" # 增量实例版本

# 托管集群信息
cloud:
  # ACG-云计算业务 > 16-云原生平台 > 服务网格 CSM
  # 访问托管集群所使用的 ak sk(属于智能云账户公有云服务网格 BCE:CSM 资源账号的 ak、sk)
  hosting:
    access_key: "ALTAK3Fm7okrwOquPgKd1oQw9K"
    secret_key: "f02673fbce3c4e34b69cbfd6d5674448"

  iamProfile: "staging"
  hostingRegion:
    bj:
      clusterId: "cce-25wbnpqi"
      clusterName: "aigw-offline"
    bd:
      clusterId: "cce-kc6n6hc1"
      clusterName: "bd-aigw-online"
    su:
      clusterId: "cce-qfchd5i7"
      clusterName: "bd-aigw-online"
    fwh:
      clusterId: ""
      clusterName: ""
    gz:
      clusterId: "cce-jsph6yru"
      clusterName: "gz-aigw-online"
      # gz 托管集APIServer关联的服务发布点，用来创建用户的服务网卡，实现用户vpc访问托管集群APIServer
      apiServerDomain: ""
    hkg:
      clusterId: ""
      clusterName: ""
    cd:
      clusterId: "cce-ltejb299"
      clusterName: "cd-aigw-online"
  vpc:
    endpoint: "http://bcc.%s.baidubce.com"
  cce:
    endpoint: "http://cce.%s.baidubce.com"
    # KubeClient 缓存配置
    kube_client_cache:
      enabled: true                    # 是否启用缓存，默认 true
      default_ttl: "10h"              # 缓存默认过期时间，默认 10 分钟
      cleanup_interval: "5h"          # 缓存清理间隔，默认 5 分钟
  blb:
    endpoint: "http://blb.%s.baidubce.com"
  eip:
    endpoint: "http://eip.%s.baidubce.com"
  cert:
    endpoint: "http://certificate.baidubce.com"
  bls:
    endpoint: "**************:8085" # 暂使用VIP，待OpenAPI上线后改为"http://bls.%s.baidubce.com:8085"
  regions:
    - bd
    - bj
    - fwh
    - gz
    - hkg
    - su

# 配置支持的服务网格版本
istio:
  standalone:
    aeraki:
      hub: "registry.baidubce.com/csm-offline/aeraki"
      imagePullPolicy: "Always"
      supportVersion: # istio 对应的 aeraki 控制平面版本
        "1.14.6": "1.2.3"
        "1.16.5": "1.3.0"
    iop:
      hub: "registry.baidubce.com/csm-offline"
    version:
      - "1.13.2"
      - "1.14.6"
      - "1.16.5"
  hosting:
    iop:
      hub: "registry.baidubce.com/csm-offline"
    version:
      - "1.14.6-baidu"
    higress:
      hub: "registry.baidubce.com/csm-offline"
      image: "higress"
      tag: "1.0.0"

# AI网关配置
aigw:
  # vpc配置
  vpc:
    hostingId: "vpc-ffwab0gw7065"
  # Redis服务配置
  redis:
    environment: "offline"  # redis环境配置参考： https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/AOhtOwLQrY/jNp_DTmrGi/GDq0RLgt4uibod
    offline:
      address: "*************"
      service_name: "redis-service.redis-aigw.svc.cluster.local"
      service_port: 8379
      password: "XxUisaxa-+"
    online:
      address: "*************"
      service_name: "redis-service.redis-aigw.svc.cluster.local"
      service_port: 8379
      password: "XxUisaxa-+"
  
  # 插件配置
  plugins:
    token_limit_plugin:
      name: "ai-token-limit"
      url: "oci://registry.baidubce.com/csm-offline/wasm-go/ai-token-ratelimit:1.0.5"
      template_path: "templates/higress/wasm/token-rate-limit.tmpl"
    key_auth_plugin:
      name: "key-auth"
      url: "oci://registry.baidubce.com/csm-offline/wasm-go/key-auth:1.0.6"
      template_path: "templates/higress/wasm/consumer-jwt-auth.tmpl"
    ai_quota_plugin:
      name: "ai-quota"
      url: "oci://registry.baidubce.com/csm-offline/wasm-go/ai-quota:1.0.8"
      template_path: "templates/higress/wasm/ai-quota.tmpl"
    ip_restriction_plugin:
      url: "oci://registry.baidubce.com/csm-offline/wasm-go/ip-restriction:20250127"
      template_path: "templates/higress/wasm/ip-restriction.tmpl"
    ai_statistics_plugin:
      name: "ai-statistics"
      url: "oci://registry.baidubce.com/csm-offline/wasm-go/ai-statistics:2.1.0"
      template_path: "templates/higress/wasm/ai-statistics.tmpl"
    ext_auth_plugin:
      name: "ext-auth"
      url: "oci://registry.baidubce.com/csm-offline/wasm-go/ext-auth:1.0.0"
      template_path: "templates/higress/wasm/ext-auth.tmpl"


mysql:
  dsnOptions: "charset=utf8&parseTime=true&loc=Local&timeout=3s&readTimeout=8s&writeTimeout=3s"
  driver: "mysql"
  user: "root"
  password: "vm531@Vrp"
  host: "bjhw-bce-cnap01.bjhw.baidu.com:8306"
  database: "bce_csm_logic_qasandbox"

eks:
  profile: "grey"
  grey:
    clusterId: "cce-v8rhd9sv"
    clusterName: "native-bce-trial-bjlsh"
  online:
    clusterId: "cce-6c2qtavn"
    clusterName: "csm-primary-bjdd"
  accountIds:
    - 0c0b3c9dbb6e41308d3bfd587d908922

local:
  # 本地开发的开关, true表示通过本地ak，sk调用cce
  dev: true
  # 是否表示跳过iam middleware, 沙盒环境需置为false
  mock: true
  mockUserID: eca97e148cb74e9683d7b7240829d1ff
  mockDomainID: eca97e148cb74e9683d7b7240829d1ff

  # 本地访问 cce 所使用的 ak sk(属于智能云账户 CCE_jpaas_测试账号 eca97e148cb74e9683d7b7240829d1ff (ACG内部测试账号))
  access_key: "ALTAKjAn3LHm0cy09dDLp3b07j"
  secret_key: "85ba393580f447579032e380c0236b90"

  # CProm 测试用
  # access_key: "e0c68be8495540f280b3e9ef03ec25d2"
  # secret_key: "b599d5f4f30e41bcb0e1482c9de65bd6"

iam:
  profile:
    staging:
      username: csm
      password: Zbg3QA69fJKfw8sAXNSWrcCuKicSe7g8
      default_domain: default
      subuser_support: true
      auth_method: password
      access_key:
      secret_key:
      token_cache_size: 1000
      secret_cache_size: 1000
      active_cache_size: 1000
      region: gz
      host: iam.gz.internal-qasandbox.baidu-int.com
      port: 80
      stshost: sts.gz.internal-qasandbox.baidu-int.com
      stsport: 8586
      iamAgent: http://gzbh-sandbox19-6271.gzbh.baidu.com:8236
  default: staging
  user_id_map:
    online:

proxy:
  enableCheckPermission: true
lanFilter:
  domainWhiteList:
    - hub.baidubce.com
    - hub-auth.baidubce.com
    - index.docker.io
    - mirror.baidubce.com
  ipWhiteList:

userWhiteList:
  host: "**********"
  endpoint: "http://user-config.internal-qasandbox.baidu-int.com:8690"
  requestPath: "/v1/settings/acl/get"
  requestBody:
    featureType: "EnableCSM"
    aclType: "AccountId"

monitor:
  iamProfile: "staging"
  profile: "internal"
  internal:
    host:
      # 本地开发调试 gz 与 bj 的 28794 端口网络不通，需要修改为 8794
      # gz 与 bj 灰度与线上端口使用 28794
      gz: "http://*************:8794"
      bj: "http://**************:8794"
      bd: "http://***********:8794"
      fwh: "http://***********:8794"
      hkg: "http://**********:8794"
      su: "http://**************:8794"
    instancesUrl: "/api/v1/cprom/instances"
    agentUrl: "/api/v1/cprom/instances/%s/binding_cluster"
    scrapeJobUrl: "/api/v1/cprom/scrape_jobs?instanceID=%s&agentID=%s"
    deleteScrapeJobUrl: "/api/v1/cprom/scrape_jobs/%s?instanceID=%s&agentID=%s"
  online:
    host: "http://cprom.%s.baidubce.com"
    instancesUrl: "/v1/instances"
    agentUrl: "/v1/instances/%s/binding_cluster"
    scrapeJobUrl: "/v1/scrape_jobs?instanceID=%s&agentID=%s"
    deleteScrapeJobUrl: "/v1/scrape_jobs/%s?instanceID=%s&agentID=%s"
  sandbox:
    host: "http://*************:8794"
    instancesUrl: "/api/v1/cprom/instances"
    agentUrl: "/api/v1/cprom/instances/%s/binding_cluster"
    scrapeJobUrl: "/api/v1/cprom/scrape_jobs?instanceID=%s&agentID=%s"
    deleteScrapeJobUrl: "/api/v1/cprom/scrape_jobs/%s?instanceID=%s&agentID=%s"
  scrape_job:
    template:
      istio: "istio_scrape_job.yaml"
      envoy: "envoy_scrape_job.yaml"
      gateway: "gateway_scrape_job.tmpl"
      hostingIstio: "hosting_istio_scrape_job.tmpl"

crd:
  allowedKind:
    - ApplicationProtocol
    - MetaRouter
    - DubboAuthorizationPolicy
    - RedisService
    - RedisDestination

users:
  userInfo:
    数云科技:
      - 35d9ca8a69a243f0b2aa5864fd1eca29
    内部混合云账号EKS/集团云:
      - 0c0b3c9dbb6e41308d3bfd587d908922
    超境汽车:
      - 735f05c2245146019c18d2d88887fc5c
    QA测试账号:
      - 1a3ad1bcb67449ad992245e690e1a442
    集度汽车:
      - 5f5ac06141d44783a7e67491f3b0edc1
      - f7ac4b5b395846389a889f7b89e9f030
    RD测试账号:
      - 144581026ccf4c419c8b1f8cc7e135fa

# 支持的集群 node 类型和版本
nodeOS:
  CentOS:
    - 7.1
    - 7.2
    - 7.3
    - 7.4
    - 7.5
    - 7.6
    - 7.7
    - 7.8
    - 7.9
  Ubuntu:
    - 16.04 LTS
    - 18.04 LTS
    - 20.04 LTS
    - 22.04 LTS