package meta

import (
	"fmt"
	"strings"
)

// TokenRateLimitMatchCondition represents the condition for token rate limiting
type TokenRateLimitMatchCondition struct {
	Type  string `json:"type" valid:"required"` // consumer, header, query_param
	Key   string `json:"key" valid:"required"`  // consumer_name, header_name, param_name
	Value string `json:"value"`                 // Required when type is header or query_param
}

// TokenRateLimitConfig represents the configuration for token rate limiting
type TokenRateLimitConfig struct {
	TimeUnit    string `json:"time_unit" valid:"required"`    // second, minute, hour, day
	TokenAmount int    `json:"token_amount" valid:"required"` // Token count, must be > 0
}

// TokenRateLimitRuleItem represents a single rate limit rule
type TokenRateLimitRuleItem struct {
	MatchCondition TokenRateLimitMatchCondition `json:"match_condition" valid:"required"`
	LimitConfig    TokenRateLimitConfig         `json:"limit_config" valid:"required"`
}

// TokenRateLimit represents the token rate limit configuration
type TokenRateLimit struct {
	Enabled   bool                     `json:"enabled"`
	RuleItems []TokenRateLimitRuleItem `json:"rule_items,omitempty"`
}

// Rewrite represents the path rewrite configuration
type Rewrite struct {
	Enabled bool   `json:"enabled"`        // 是否启用路径重写
	Path    string `json:"path,omitempty"` // 重写后的路径，当enabled=true时必填
}

// MatchRule represents the matching rules for a route
type MatchRule struct {
	PathRule struct {
		MatchType     string `json:"matchType" valid:"required"` // prefix, exact
		Value         string `json:"value" valid:"required"`     // path value
		CaseSensitive bool   `json:"caseSensitive"`              // case sensitive flag
	} `json:"pathRule" valid:"required"`
	Methods []string `json:"methods"` // HTTP methods
	Headers []struct {
		Key       string `json:"key" valid:"required"`       // header name
		MatchType string `json:"matchType" valid:"required"` // prefix, exact
		Value     string `json:"value" valid:"required"`     // header value
	} `json:"headers"`
	QueryParams []struct {
		Key       string `json:"key" valid:"required"`       // query param name
		MatchType string `json:"matchType" valid:"required"` // prefix, exact
		Value     string `json:"value" valid:"required"`     // query param value
	} `json:"queryParams"`
}

// TargetService represents a single target service configuration
type TargetService struct {
	ServiceSource        string `json:"serviceSource" valid:"required"` // CCE
	ServiceName          string `json:"serviceName" valid:"required"`   // service name
	Namespace            string `json:"namespace" valid:"required"`     // namespace
	ServicePort          int    `json:"servicePort" valid:"required"`   // service port
	LoadBalanceAlgorithm string `json:"loadBalanceAlgorithm"`           // round-robin, least-conn, random, consistent-hash
	RequestRatio         int    `json:"requestRatio,omitempty"`         // for ratio distribution strategy
	ModelName            string `json:"modelName,omitempty"`            // for model_name distribution strategy
	Weight               int    `json:"weight,omitempty"`               // for weighted distribution strategy
	HashType             string `json:"hashType,omitempty"`             // 新增：哈希一致性类型
	HashKey              string `json:"hashKey,omitempty"`              // 新增：哈希一致性参数
}

// TrafficDistributionStrategy represents the available traffic distribution strategies
const (
	TrafficDistributionRatio      = "ratio"       // Distribute traffic based on request ratio
	TrafficDistributionModelName  = "model_name"  // Distribute traffic based on model name
	TrafficDistributionWeighted   = "weighted"    // Distribute traffic based on weight
	TrafficDistributionRoundRobin = "round-robin" // Round-robin distribution
)

// AIRouteRequest represents the request body for creating a route
type AIRouteRequest struct {
	RouteName                   string         `json:"routeName" valid:"required"`            // route name
	SrcProduct                  string         `json:"srcProduct"`                            // source product
	MatchRules                  MatchRule      `json:"matchRules" valid:"required"`           // matching rules
	MultiService                bool           `json:"multiService"`                          // enable multi-service mode
	TrafficDistributionStrategy string         `json:"trafficDistributionStrategy,omitempty"` // ratio, model_name, weighted, round-robin
	TargetService               interface{}    `json:"targetService" valid:"required"`        // single service or array of services
	Rewrite                     Rewrite        `json:"rewrite"`                               // path rewrite configuration
	AuthEnabled                 bool           `json:"authEnabled"`                           // enable authentication
	AllowedConsumers            []string       `json:"allowedConsumers"`                      // allowed consumers
	TokenRateLimit              TokenRateLimit `json:"tokenRateLimit"`                        // token rate limit config
	TimeoutPolicy               *TimeoutPolicy `json:"timeoutPolicy,omitempty"`
	RetryPolicy                 *RetryPolicy   `json:"retryPolicy,omitempty"`
}

// GetSingleTargetService returns the target service for single service mode
func (r *AIRouteRequest) GetSingleTargetService() (*TargetService, error) {
	if r.MultiService {
		return nil, fmt.Errorf("cannot get single target service in multi-service mode")
	}

	// Convert interface{} to TargetService
	serviceMap, ok := r.TargetService.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid target service format")
	}

	service := &TargetService{}
	if val, exists := serviceMap["serviceSource"]; exists {
		if str, ok := val.(string); ok {
			service.ServiceSource = str
		}
	}
	if val, exists := serviceMap["serviceName"]; exists {
		if str, ok := val.(string); ok {
			service.ServiceName = str
		}
	}
	if val, exists := serviceMap["namespace"]; exists {
		if str, ok := val.(string); ok {
			service.Namespace = str
		}
	}
	if val, exists := serviceMap["servicePort"]; exists {
		if port, ok := val.(float64); ok {
			service.ServicePort = int(port)
		}
	}
	if val, exists := serviceMap["loadBalanceAlgorithm"]; exists {
		if str, ok := val.(string); ok {
			service.LoadBalanceAlgorithm = str
			if str == "consistent-hash" {
				service.HashType = serviceMap["hashType"].(string)
				service.HashKey = serviceMap["hashKey"].(string)
			}
		}
	}

	return service, nil
}

// GetMultiTargetServices returns the target services for multi-service mode
func (r *AIRouteRequest) GetMultiTargetServices() ([]TargetService, error) {
	if !r.MultiService {
		return nil, fmt.Errorf("cannot get multi target services in single-service mode")
	}

	// Convert interface{} to []TargetService
	serviceArray, ok := r.TargetService.([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid target services format")
	}

	var services []TargetService
	for _, item := range serviceArray {
		serviceMap, ok := item.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("invalid service item format")
		}

		service := TargetService{}
		if val, exists := serviceMap["serviceSource"]; exists {
			if str, ok := val.(string); ok {
				service.ServiceSource = str
			}
		}
		if val, exists := serviceMap["serviceName"]; exists {
			if str, ok := val.(string); ok {
				service.ServiceName = str
			}
		}
		if val, exists := serviceMap["namespace"]; exists {
			if str, ok := val.(string); ok {
				service.Namespace = str
			}
		}
		if val, exists := serviceMap["servicePort"]; exists {
			if port, ok := val.(float64); ok {
				service.ServicePort = int(port)
			}
		}
		if val, exists := serviceMap["loadBalanceAlgorithm"]; exists {
			if str, ok := val.(string); ok {
				service.LoadBalanceAlgorithm = str
				if str == "consistent-hash" {
					service.HashType = serviceMap["hashType"].(string)
					service.HashKey = serviceMap["hashKey"].(string)
				}
			}
		}
		if val, exists := serviceMap["requestRatio"]; exists {
			if ratio, ok := val.(float64); ok {
				service.RequestRatio = int(ratio)
			}
		}
		if val, exists := serviceMap["modelName"]; exists {
			if str, ok := val.(string); ok {
				service.ModelName = str
			}
		}

		services = append(services, service)
	}

	return services, nil
}

// AIRouteResponse represents the response body for a route
type AIRouteResponse struct {
	RouteName                   string                 `json:"routeName"`                             // route name
	MatchPath                   string                 `json:"matchPath"`                             // match path
	MatchRules                  *MatchRule             `json:"matchRules,omitempty"`                  // 匹配规则，不包含rewrite配置
	ServiceName                 string                 `json:"serviceName"`                           // service name (multi-service for multi mode)
	CreateTime                  string                 `json:"createTime"`                            // create time
	UpdateTime                  string                 `json:"updateTime,omitempty"`                  // update time
	MultiService                bool                   `json:"multiService"`                          // multi-service flag
	TrafficDistributionStrategy string                 `json:"trafficDistributionStrategy,omitempty"` // traffic distribution strategy
	Rewrite                     Rewrite                `json:"rewrite"`                               // path rewrite configuration
	TargetService               interface{}            `json:"targetService"`                         // single service or array of services
	AuthEnabled                 bool                   `json:"authEnabled"`                           // enable authentication
	AllowedConsumers            []string               `json:"allowedConsumers,omitempty"`            // allowed consumers
	TokenRateLimit              TokenRateLimitResponse `json:"tokenRateLimit,omitempty"`              // token rate limit response
	TimeoutPolicy               *TimeoutPolicy         `json:"timeoutPolicy,omitempty"`
	RetryPolicy                 *RetryPolicy           `json:"retryPolicy,omitempty"`
}

// TokenRateLimitResponse represents the token rate limit in the response
type TokenRateLimitResponse struct {
	RuleName  string                   `json:"rule_name"`
	Enabled   bool                     `json:"enabled"`
	RuleItems []TokenRateLimitRuleItem `json:"rule_items,omitempty"`
}

// TokenRateLimitTemplateData Token限流插件模板数据
type TokenRateLimitTemplateData struct {
	PluginName       string                   `json:"pluginName"`
	Namespace        string                   `json:"namespace"`
	RuleName         string                   `json:"ruleName"`
	RouteName        string                   `json:"routeName"`
	RuleItems        []TokenRateLimitRuleItem `json:"ruleItems"`
	PluginURL        string                   `json:"pluginURL"`
	RedisServiceName string                   `json:"redisServiceName"`
	RedisServicePort int                      `json:"redisServicePort"`
	RedisPassword    string                   `json:"redisPassword"`
}

// AIQuotaTemplateData AI配额插件模板数据
type AIQuotaTemplateData struct {
	Namespace        string   `json:"namespace"`
	RouteNames       []string `json:"routeNames"`
	RedisKeyPrefix   string   `json:"redisKeyPrefix"`
	PluginName       string   `json:"pluginName"`
	PluginURL        string   `json:"pluginURL"`
	RedisServiceName string   `json:"redisServiceName"`
	RedisServicePort int      `json:"redisServicePort"`
	RedisPassword    string   `json:"redisPassword"`
}

// AIStatisticsTemplateData AI可观测插件模板数据
type AIStatisticsTemplateData struct {
	Namespace  string `json:"namespace"`  // 命名空间
	PluginName string `json:"pluginName"` // 插件名称，默认为"ai-statistics"
	PluginURL  string `json:"pluginURL"`  // 插件URL
}

// ValidateTrafficDistribution validates the traffic distribution strategy and target services
func (r *AIRouteRequest) ValidateTrafficDistribution() error {
	if !r.MultiService {
		return nil
	}

	services, err := r.GetMultiTargetServices()
	if err != nil {
		return err
	}

	switch r.TrafficDistributionStrategy {
	case TrafficDistributionRatio:
		totalRatio := 0
		for _, service := range services {
			if service.RequestRatio <= 0 {
				return fmt.Errorf("request ratio must be positive for ratio distribution")
			}
			totalRatio += service.RequestRatio
		}
		if totalRatio != 100 {
			return fmt.Errorf("total request ratio must equal 100 for ratio distribution")
		}
	case TrafficDistributionModelName:
		modelNames := make(map[string]bool)
		for _, service := range services {
			if service.ModelName == "" {
				return fmt.Errorf("model name is required for model_name distribution")
			}
			if modelNames[service.ModelName] {
				return fmt.Errorf("duplicate model name found: %s", service.ModelName)
			}
			modelNames[service.ModelName] = true
		}
	case TrafficDistributionWeighted:
		totalWeight := 0
		for _, service := range services {
			if service.Weight <= 0 {
				return fmt.Errorf("weight must be positive for weighted distribution")
			}
			totalWeight += service.Weight
		}
		if totalWeight == 0 {
			return fmt.Errorf("total weight must be greater than 0 for weighted distribution")
		}
	case TrafficDistributionRoundRobin:
		// No additional validation needed for round-robin
	default:
		return fmt.Errorf("invalid traffic distribution strategy: %s", r.TrafficDistributionStrategy)
	}

	return nil
}

// ValidateRewriteConfig validates the rewrite configuration
func (r *AIRouteRequest) ValidateRewriteConfig() error {
	if r.Rewrite.Enabled {
		if r.Rewrite.Path == "" {
			return fmt.Errorf("rewrite path is required when rewrite is enabled")
		}
		if !strings.HasPrefix(r.Rewrite.Path, "/") {
			return fmt.Errorf("rewrite path must start with '/'")
		}
	}

	return nil
}

type TimeoutPolicy struct {
	Enabled bool `json:"enabled"`
	Timeout int  `json:"timeout"` // 单位：秒
}

type RetryPolicy struct {
	Enabled         bool   `json:"enabled"`
	RetryConditions string `json:"retryConditions"`
	NumRetries      int    `json:"numRetries"`
}
