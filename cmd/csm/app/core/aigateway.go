package core

import (
	"bytes"
	"context"
	"encoding/hex"
	"fmt"
	"math"
	"math/rand"
	"net/http"
	"os"
	"path"
	"sort"
	"strconv"
	"strings"
	"text/template"
	"time"

	"github.com/baidubce/bce-sdk-go/model"
	cceV2 "github.com/baidubce/bce-sdk-go/services/cce/v2"
	eipSDK "github.com/baidubce/bce-sdk-go/services/eip"
	"github.com/spf13/viper"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/command"
	kubeErrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/baidubce/bce-sdk-go/services/endpoint"
	blbService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/blb/service"
	bceUtil "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/bce/util"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/apimachinery/pkg/util/yaml"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/csmlog"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/internal"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/constants"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/kube"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/util/object"

	"github.com/pkg/errors"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/csm/iam"
	csmErr "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/error"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/model/meta"
	reg "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/region"
	csmContext "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/context"

	istionetworkingv1alpha3 "istio.io/api/networking/v1alpha3"
	v1alpha3 "istio.io/client-go/pkg/apis/networking/v1alpha3"
	istioclientset "istio.io/client-go/pkg/clientset/versioned"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/client-go/dynamic"

	"google.golang.org/protobuf/types/known/durationpb"
	redisService "icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/service/redis"
)

// CreateAIGateway 创建AI网关实例
func (core *APIServerCore) CreateAIGateway(ctx csmContext.CsmContext) (error error) {
	// 获取账户ID
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// 绑定请求参数
	aiGatewayReq := &meta.AIGatewayRequest{}
	if err = ctx.Bind(aiGatewayReq); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 生成网关模型
	gatewayModel, err := aiGatewayReq.ToGatewayModel()
	if err != nil {
		return csmErr.NewInvalidParameterValueException(err.Error())
	}

	// 设置参数值
	region := ctx.Get(reg.ContextRegion).(string)
	srcProduct := aiGatewayReq.SrcProduct
	gatewayModel.Region = region
	gatewayModel.AccountID = accountId
	gatewayModel.VpcNetworkID = aiGatewayReq.VpcId
	gatewayModel.Description = aiGatewayReq.Description
	gatewayModel.DeletionProtection = aiGatewayReq.DeleteProtection

	// 生成网关ID
	gatewayUUID, err := core.GatewayService.GenerateGatewayID(ctx)
	if err != nil {
		return err
	}
	// 动态生成namespace，使用网关ID
	gatewayNamespace := fmt.Sprintf("istio-system-%s", gatewayUUID)
	gatewayModel.Namespace = gatewayNamespace
	gatewayModel.GatewayUUID = gatewayUUID
	gatewayModel.InstanceUUID = gatewayUUID

	// 如果有关联集群，预先保存关联信息到网关实例中
	if len(aiGatewayReq.Clusters) > 0 && len(aiGatewayReq.Clusters[0].ClusterId) > 0 {
		clusterInfo := aiGatewayReq.Clusters[0]

		// srcProduct 为 aibox 时，需要检查幂等性，同一个 clusterId 只能创建一个来源是 aibox 的实例
		if srcProduct == constants.AIGatewayProductAibox {
			ctx.CsmLogger().Infof("srcProduct为aibox，检查clusterId %s 的幂等性", clusterInfo.ClusterId)

			// 查询是否已存在相同 clusterId 且 srcProduct 为 aibox 的实例
			existingGateways, err := core.checkExistingAiboxInstance(ctx, accountId, region, clusterInfo.ClusterId)
			if err != nil {
				ctx.CsmLogger().Errorf("检查现有aibox实例失败: %v", err)
				return csmErr.NewDBOperationException(err)
			}

			if len(existingGateways) > 0 {
				instanceId := existingGateways[0].GatewayUUID
				ctx.CsmLogger().Warnf("clusterId %s 已存在aibox实例: %s", clusterInfo.ClusterId, existingGateways[0].GatewayUUID)
				return ctx.JSON(http.StatusOK, map[string]interface{}{
					"success":    true,
					"status":     http.StatusOK,
					"instanceId": instanceId,
				})
			}
		}

		// 将第一个集群信息设置为网关实例的关联集群
		gatewayModel.AddedClusterID = clusterInfo.ClusterId
		gatewayModel.AddedClusterName = clusterInfo.ClusterName
		gatewayModel.RelationTime = time.Now().Format("2006-01-02 15:04:05")
		ctx.CsmLogger().Infof("associating cluster %s with gateway %s during creation",
			clusterInfo.ClusterId, gatewayUUID)
	}

	// 创建k8s客户端
	hostedClusterId, hostedClusterName := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	gatewayModel.HostedClusterID = hostedClusterId
	gatewayModel.HostedClusterName = hostedClusterName
	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		return errors.Wrap(err, "failed to create cluster client")
	}

	vpcCidr := gatewayModel.VpcCIDR

	// 创建命名空间
	namespace := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: gatewayNamespace,
		},
	}
	_, err = hostingClient.Kube().CoreV1().Namespaces().Create(context.TODO(), namespace, metav1.CreateOptions{})
	if err != nil && !strings.Contains(err.Error(), "already exists") {
		ctx.CsmLogger().Errorf("failed to create namespace %s, err=%v", gatewayNamespace, err)
		return errors.Wrap(err, "failed to create namespace")
	}

	// 读取Higress安装模板
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("failed to get current directory, err=%v", err)
		return errors.Wrap(err, "failed to get current directory")
	}

	// 使用新的模板文件
	higressTemplatePath := path.Join(pwd, "templates/higress/hosting/higress-install.tmpl")
	templateData, err := os.ReadFile(higressTemplatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to read Higress template file, err=%v", err)
		return errors.Wrap(err, "failed to read Higress template file")
	}

	// 给弹性网卡创建安全组规则
	securityGroupArgs := &meta.CreateSecurityGroupRuleArgs{
		Name:  gatewayUUID + "-sg-rule",
		Desc:  "security group rule for aigw",
		VpcId: gatewayModel.VpcNetworkID,
	}
	securityGroupId, err := core.VpcService.CreateSecurityGroupRule(ctx, securityGroupArgs, region)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create security group rule, err=%v", err)
		return errors.Wrap(err, "failed to create security group rule")
	}
	gatewayModel.SecurityGroupId = securityGroupId
	//// 准备模板数据
	data := meta.HigressTemplateData{
		Namespace:        gatewayNamespace,
		AccountId:        accountId,
		SubnetId:         aiGatewayReq.SubnetId,
		SecurityGroupIds: securityGroupId,
		VpcCidr:          vpcCidr,
		IsInternal:       aiGatewayReq.IsInternal,
		Replicas:         aiGatewayReq.Replicas,
	}

	// 渲染模板
	tmpl, err := template.New("higress").Parse(string(templateData))
	if err != nil {
		ctx.CsmLogger().Errorf("failed to parse template, err=%v", err)
		return errors.Wrap(err, "failed to parse template")
	}

	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, data); err != nil {
		ctx.CsmLogger().Errorf("failed to render template, err=%v", err)
		return errors.Wrap(err, "failed to render template")
	}

	// 入库
	err = core.aiIngressService.NewGateway(ctx, gatewayModel)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create AI gateway in database: %v", err)
		return err
	}
	// 创建kubeconfig ConfigMap
	kubeconfigData := map[string]string{
		"kubeconfig": "",
	}
	err = core.aiIngressService.CreateConfigMap(ctx, hostingClient, gatewayNamespace, "kubeconfig-configmap", kubeconfigData)
	if err != nil {
		csmlog.Errorf("Failed to create kubeconfig ConfigMap: %v", err)
		return err
	}

	// 安装Higress
	go func() {

		higressObjects, err := object.ManifestK8sObject(ctx, rendered.String())
		if err != nil {
			ctx.CsmLogger().Errorf("failed to parse Higress template manifest, err=%v", err)
		}
		err = kube.CreateResources(ctx, hostingClient, higressObjects)
		if err != nil {
			ctx.CsmLogger().Errorf("failed to create Higress resources, err=%v", err)
		}

		// 关联集群到Higress，但不再进行数据库操作
		if len(aiGatewayReq.Clusters) > 0 {
			// 遍历所有关联集群，配置Higress连接
			for _, clusterInfo := range aiGatewayReq.Clusters {
				ctx.CsmLogger().Infof("applying cluster %s configuration to Higress", clusterInfo.ClusterId)

				// 获取集群kubeconfig
				kct := cceV2.KubeConfigTypeInternal
				if viper.GetBool("local.dev") {
					ctx.CsmLogger().Infof("*** local develop mode to get NewClient ***")
					kct = cceV2.KubeConfigTypePublic
				}

				kubeConfig, err := core.cceService.GetCCEClusterKubeConfigByClusterUUID(
					ctx, region, clusterInfo.ClusterId, kct, meta.StandaloneMeshType)
				if err != nil {
					ctx.CsmLogger().Errorf("failed to get kubeconfig for cluster %s, err=%v", clusterInfo.ClusterId, err)
					continue
				}

				// 为每个集群创建临时kubeconfig文件
				pwd, err := os.Getwd()
				if err != nil {
					ctx.CsmLogger().Errorf("failed to get current directory, err=%v", err)
					continue
				}

				kubeConfigName := fmt.Sprintf("%s-%s-temp.yaml", region, clusterInfo.ClusterId)
				kubeConfigPath := path.Join(pwd, "templates/higress/hosting", kubeConfigName)

				err = os.WriteFile(kubeConfigPath, []byte(kubeConfig), 0644)
				if err != nil {
					ctx.CsmLogger().Errorf("failed to write kubeconfig file, err=%v", err)
					continue
				}

				vpcKubeConfig, err := core.cceService.GetCCEClusterKubeConfigByClusterUUID(
					ctx, region, clusterInfo.ClusterId, cceV2.KubeConfigTypeVPC, meta.StandaloneMeshType)
				vpcKubeConfigName := fmt.Sprintf("%s-%s-vpc-temp.yaml", region, clusterInfo.ClusterId)
				vpcKubeConfigPath := path.Join(pwd, "templates/higress/hosting", vpcKubeConfigName)
				err = os.WriteFile(vpcKubeConfigPath, []byte(vpcKubeConfig), 0644)
				if err != nil {
					ctx.CsmLogger().Errorf("failed to write kubeconfig file, err=%v", err)
					continue
				}

				restConfig, err := clientcmd.BuildConfigFromFlags("", vpcKubeConfigPath)
				if err != nil {
					ctx.CsmLogger().Errorf("failed to create k8s rest client: %s", err)
					continue
				}

				vpcAPIServer := restConfig.Host

				// 创建remote-secret
				remoteSecretName := fmt.Sprintf("%s-%s", region, clusterInfo.ClusterId)
				istioctlBin := path.Join(pwd, "templates/higress/hosting/bin", util.GetIstioCtl(ctx))

				// 使用istioctl创建remote-secret
				createRemoteSecretCmd := fmt.Sprintf("%s create-remote-secret --kubeconfig=%s --name=%s "+
					"--type=remote --server=%s --namespace %s",
					istioctlBin, kubeConfigPath, remoteSecretName, vpcAPIServer, gatewayNamespace)

				// 执行命令，使用command包中的方法
				cmdResult, cmdErr, err := command.ExecCmdOut(ctx, createRemoteSecretCmd)
				if len(cmdErr) > 0 || err != nil {
					ctx.CsmLogger().Errorf("istioctl manifest generate errStr %s err %v", string(cmdErr), err)
					continue
				}

				objectsList, err := object.ManifestK8sObject(ctx, string(cmdResult))
				if err != nil {
					ctx.CsmLogger().Errorf("failed to parse manifest: %v", err)
					continue
				}

				err = kube.CreateOrUpdateK8sResource(ctx, hostingClient, objectsList)
				if err != nil {
					ctx.CsmLogger().Errorf("Failed to create remote secret: %v", err)
					continue
				}

				// 清理临时文件
				os.Remove(kubeConfigPath)
				os.Remove(vpcKubeConfigPath)

				ctx.CsmLogger().Infof("Successfully configured cluster %s with AI gateway instance %s",
					clusterInfo.ClusterId, gatewayUUID)
			}
		}
		// 根据srcProduct条件决定是否创建服务发布点和用户VPC端点
		if srcProduct != "aibox" {
			ctx.CsmLogger().Infof("srcProduct不是aibox")
			// 创建基础的AI可观测插件
			err = core.createBaseAIStatisticsPlugin(ctx, gatewayNamespace, hostingClient)
			if err != nil {
				ctx.CsmLogger().Errorf("failed to create base AI Statistics plugin, err=%v", err)
			}
			// 创建基础的Key Auth插件
			err = core.createBaseKeyAuthPlugin(ctx, gatewayNamespace, hostingClient)
			if err != nil {
				ctx.CsmLogger().Errorf("failed to create base Key Auth plugin, err=%v", err)
			}

			// 创建基础的AI配额插件
			err = core.createBaseAIQuotaPlugin(ctx, gatewayNamespace, hostingClient)
			if err != nil {
				ctx.CsmLogger().Errorf("failed to create base AI Quota plugin, err=%v", err)
			}
			// 在Higress资源创建完成后，创建服务发布点和用户VPC端点
			err = core.createGatewayServiceAndEndpoint(ctx, region, hostedClusterId, gatewayUUID, aiGatewayReq)
			if err != nil {
				ctx.CsmLogger().Errorf("failed to create service and endpoint, err=%v", err)
			}
		} else {
			ctx.CsmLogger().Infof("srcProduct为aibox，跳过创建服务发布点和用户VPC端点")
		}
	}()

	// 返回成功响应
	requestId := fmt.Sprintf("req-%s", time.Now().Format("20060102150405"))
	taskId := fmt.Sprintf("task-%s", time.Now().Format("20060102150405"))

	response := &meta.AIGatewayResponse{
		Success: true,
		Status:  http.StatusOK,
		Result: struct {
			InstanceId string `json:"instanceId"`
			RequestId  string `json:"requestId"`
			TaskId     string `json:"taskId"`
		}{
			InstanceId: gatewayUUID,
			RequestId:  requestId,
			TaskId:     taskId,
		},
	}

	return ctx.JSON(http.StatusOK, response.Result)
}

// createGatewayServiceAndEndpoint 在托管集群创建服务发布点、用户集群创建服务网卡
func (core *APIServerCore) createGatewayServiceAndEndpoint(ctx csmContext.CsmContext, region,
	hostedClusterId, gatewayUUID string, aiGatewayReq *meta.AIGatewayRequest) error {
	// 轮询等待获取gateway对应的LoadBalancer的ID
	loadBalancerID, err := core.getGatewayBlbId(ctx, gatewayUUID, hostedClusterId)
	if err != nil {
		ctx.CsmLogger().Errorf("getGatewayBlbId error %v", err)
		return err
	}
	ctx.CsmLogger().Infof("the value of LoadBalancerID is [%s]", loadBalancerID)

	// 托管集群创建服务发布点
	name := gatewayUUID
	serviceName := strings.ReplaceAll(name, constants.SplitSlash, "")
	description := fmt.Sprintf("the endpoint for %s instance", gatewayUUID)

	ctx.CsmLogger().Infof("creating BlbService region=%s, name=%s, description=%s, serviceName=%s",
		region, name, constants.CsmBlbServiceDefaultDescribe, serviceName)

	args := &blbService.CreateBlbServiceArgs{
		ClientToken: bceUtil.GetClientToken(),
		Name:        name,
		Description: description,
		ServiceName: serviceName,
		InstanceID:  loadBalancerID,
		AuthList:    blbService.NewDefaultAuth(),
	}

	createBlbServiceResult, err := core.BlbService.CreateBlbService(ctx, args, region)
	if err != nil {
		ctx.CsmLogger().Errorf("createBlbService in blb service error %v", err)
		return err
	}
	service := createBlbServiceResult.Service

	// 轮询等待服务发布点创建成功
	getBlbServiceArgs := &blbService.GetBlbServiceArgs{
		Service: service,
	}
	getErr := wait.PollImmediate(constants.PollInternal, constants.Internal, func() (done bool, err error) {
		getBlbServiceResult, err := core.BlbService.GetBlbService(ctx, getBlbServiceArgs, region)
		if err != nil {
			ctx.CsmLogger().Infof("GetBlbService in blb service error %v", err)
			return false, err
		}
		instanceID := getBlbServiceResult.InstanceId
		ctx.CsmLogger().Infof("GetBlbService instanceID is %s", instanceID)
		return len(instanceID) > 0, nil
	})
	if getErr != nil {
		ctx.CsmLogger().Errorf("GetBlbService in blb service error %v", getErr)
		return getErr
	}

	// 用户集群创建服务网卡
	vpcEndpointName := gatewayUUID
	vpcEndpointService := service

	createEndpointArgs := &endpoint.CreateEndpointArgs{
		ClientToken: bceUtil.GetClientToken(),
		VpcId:       aiGatewayReq.VpcId,
		Name:        vpcEndpointName,
		SubnetId:    aiGatewayReq.SubnetId,
		Service:     vpcEndpointService,
		Description: description,
		Billing: &endpoint.Billing{
			PaymentTiming: "Postpaid",
		},
	}

	// tags 打上标签，key为csm-hosting，value为csm实例ID
	snicTag := []model.TagModel{{
		TagKey:   "aigw-hosting",
		TagValue: gatewayUUID,
	}}

	createEip := ""
	endpointIp := ""
	if aiGatewayReq.IsInternal == "false" {
		createEipArgs := &eipSDK.CreateEipArgs{
			BandWidthInMbps: 1,
			Billing: &eipSDK.Billing{
				PaymentTiming: "Postpaid",
				BillingMethod: "ByTraffic",
			},
			Tags: snicTag,
		}
		eipResult, createErr := core.eipService.CreateEIP(ctx, createEipArgs, region)
		createEip = eipResult.Eip

		createEndpointEIPArgs := &meta.CreateEndpointArgs{
			ClientToken: bceUtil.GetClientToken(),
			VpcId:       aiGatewayReq.VpcId,
			Name:        vpcEndpointName,
			SubnetId:    aiGatewayReq.SubnetId,
			Service:     vpcEndpointService,
			Description: description,
			Billing: &meta.Billing{
				PaymentTiming: "Postpaid",
			},
			Tags: snicTag,
			Eip:  createEip,
		}
		createEndpointEIPResult, createErr := core.VpcService.CreateEndpointWithEip(ctx, createEndpointEIPArgs, region)
		if createErr != nil {
			ctx.CsmLogger().Errorf("createEndpointEIP in EIP endpoint error %v", createErr)
			//return createErr
		}
		ctx.CsmLogger().Infof("the result of vpc endpoint with eip %v", createEndpointEIPResult)
		endpointIp = createEndpointEIPResult.IpAddress
	} else {
		createEndpointResult, err := core.VpcService.CreateEndpoint(ctx, createEndpointArgs, region)
		if err != nil {
			ctx.CsmLogger().Errorf("createEndpoint in vpc endpoint error %v", err)
			return err
		}
		ctx.CsmLogger().Infof("the result of vpc endpoint %v", createEndpointResult)
		endpointIp = createEndpointResult.IpAddress
	}

	// 更新网关模型中的IP地址信息
	err = core.updateGatewayEndpointAddress(ctx, gatewayUUID, endpointIp, createEip)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to update gateway endpoint address, err=%v", err)
		return err
	}

	return nil
}

// getGatewayBlbId 获取Gateway LoadBalancer ID，轮询等待直到获取成功
func (core *APIServerCore) getGatewayBlbId(ctx csmContext.CsmContext, gatewayUUID, hostedClusterId string) (string, error) {
	namespace := fmt.Sprintf("istio-system-%s", gatewayUUID)

	// 创建k8s客户端
	region := ctx.Get(reg.ContextRegion).(string)
	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		return "", errors.Wrap(err, "failed to create cluster client")
	}

	// 轮询等待获取LoadBalancer ID
	var blbID string
	err = wait.PollImmediate(10*time.Second, 5*time.Minute, func() (bool, error) {
		// 查询对应namespace下的gateway-internal服务
		svc, err := hostingClient.Kube().CoreV1().Services(namespace).Get(context.TODO(),
			"higress-gateway", metav1.GetOptions{})
		if err != nil {
			if k8serrors.IsNotFound(err) {
				ctx.CsmLogger().Infof("Service higress-gateway not found yet, waiting...")
				return false, nil
			}
			return false, err
		}

		if svc.Spec.Type == corev1.ServiceTypeLoadBalancer {
			annotations := svc.ObjectMeta.Annotations
			if annotations != nil {
				blbID = annotations[constants.CceLoadBalancerID]
				if blbID != "" {
					ctx.CsmLogger().Infof("Found LoadBalancer ID: %s", blbID)
					return true, nil
				}
			}
		}

		ctx.CsmLogger().Infof("LoadBalancer ID not yet available, waiting...")
		return false, nil
	})

	if err != nil {
		return "", errors.Wrap(err, "timeout waiting for LoadBalancer ID")
	}

	if blbID == "" {
		return "", errors.New("BLB ID not found for gateway service after polling")
	}

	return blbID, nil
}

// updateGatewayEndpointAddress 更新网关模型中的IP地址信息
func (core *APIServerCore) updateGatewayEndpointAddress(ctx csmContext.CsmContext, gatewayUUID, ipAddress string, eipAddress string) error {
	// 获取网关实例
	gatewayInfo, err := core.aigatewayModel.GetAIGatewayInfo(ctx, gatewayUUID, gatewayUUID)
	if err != nil {
		return err
	}

	// 更新数据库记录
	updateGateway := **gatewayInfo
	if eipAddress != "" {
		updateGateway.PublicAccessible = true
	}
	updateGateway.AccessAddress = ipAddress + "," + eipAddress
	gatewayPtr := &updateGateway
	err = core.aigatewayModel.UpdateAIGateway(ctx, gatewayInfo, &gatewayPtr)
	if err != nil {
		return err
	}

	return nil
}

// ListCceCluster 获取 cce 集群列表中的数据
func (core *APIServerCore) ListCceClusterByVPC(ctx csmContext.CsmContext) error {
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取VPC ID
	vpcId := ctx.QueryParam("vpcId")
	if vpcId == "" {
		return csmErr.NewMissingParametersException("vpcId")
	}
	cceCluster, err := core.InstancesService.GetAllCceClusterByRegion(ctx, region)
	if err != nil {
		ctx.CsmLogger().Errorf("ListCceCluster error %v", err)
		return err
	}
	res := make([]*internal.CceCluster, 0)
	for _, cluster := range cceCluster {
		cceClusterView, cceErr := internal.ToMeshCluster(&cluster)
		if cceErr != nil {
			return cceErr
		}
		if cceClusterView.VpcId != vpcId {
			continue
		}
		res = append(res, cceClusterView)
	}
	return ctx.JSON(http.StatusOK, res)
}

// AssociateClusterWithAIGateway 添加关联集群到AI网关实例
func (core *APIServerCore) AssociateClusterWithAIGateway(ctx csmContext.CsmContext) (error error) {
	// 获取实例ID
	gatewayUUID := ctx.Param("InstanceId")
	if gatewayUUID == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}

	request := &meta.AssociateClusterRequest{}
	if err := ctx.Bind(request); err != nil {
		csmlog.Infof("request=%+v", request)
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	err := core.aiIngressService.AddClusterList(ctx, request)
	if err != nil {
		return err
	}
	// 返回成功响应
	response := &meta.AssociateClusterResponse{
		Status:  http.StatusOK,
		Message: "Clusters added successfully",
	}

	return ctx.JSON(http.StatusOK, response)
}

// DeleteAIGateway 删除AI网关实例
func (core *APIServerCore) DeleteAIGateway(ctx csmContext.CsmContext) error {
	// 获取实例ID
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}

	// 调用服务层删除网关实例
	err := core.aiIngressService.DeleteAIGateway(ctx, instanceId)
	if err != nil {
		return err
	}

	// 返回成功响应
	return ctx.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"status":  http.StatusOK,
		"result":  nil,
	})
}

// GetAIGatewayDetail 查询AI网关实例详情
func (core *APIServerCore) GetAIGatewayDetail(ctx csmContext.CsmContext) error {
	// 获取实例ID
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}
	srcProduct := ctx.QueryParam("srcProduct")
	ctx.CsmLogger().Infof("查询实例详情，instanceId=%s，srcProduct=%s", instanceId, srcProduct)

	// 调用服务层获取网关详情
	gatewayDetail, err := core.aiIngressService.GetAIGatewayDetail(ctx, instanceId, srcProduct)
	if err != nil {
		return err
	}

	// 构建响应
	response := struct {
		Success bool            `json:"success"`
		Status  int             `json:"status"`
		Data    *meta.AiGateway `json:"data"`
	}{
		Success: true,
		Status:  http.StatusOK,
		Data:    gatewayDetail,
	}

	return ctx.JSON(http.StatusOK, response.Data)
}

// GetAllIngressInstances 查询AI网关实例列表
func (core *APIServerCore) GetAllIngressInstances(ctx csmContext.CsmContext) (error error) {
	// 获取分页参数
	pageNo := 1
	pageSize := 10
	orderBy := "createTime"
	order := "desc"
	keyword := ""
	status := ""

	// 尝试从查询参数中获取分页信息
	if pageNoStr := ctx.QueryParam("pageNo"); pageNoStr != "" {
		if val, err := strconv.Atoi(pageNoStr); err == nil && val > 0 {
			pageNo = val
		}
	}
	if pageSizeStr := ctx.QueryParam("pageSize"); pageSizeStr != "" {
		if val, err := strconv.Atoi(pageSizeStr); err == nil && val > 0 {
			pageSize = val
		}
	}
	if orderByStr := ctx.QueryParam("orderBy"); orderByStr != "" {
		orderBy = orderByStr
	}
	if orderStr := ctx.QueryParam("order"); orderStr != "" {
		order = orderStr
	}
	if keywordStr := ctx.QueryParam("keyword"); keywordStr != "" {
		keyword = keywordStr
	}
	if statusStr := ctx.QueryParam("status"); statusStr != "" {
		// 验证状态值是否有效
		if statusStr == constants.AIGatewayStatusRunning || statusStr == constants.AIGatewayStatusCreating {
			status = statusStr
			ctx.CsmLogger().Infof("根据实例运行状态筛选: %s", status)
		} else {
			ctx.CsmLogger().Warnf("无效的状态筛选条件: %s，忽略该参数", statusStr)
		}
	}

	// 获取账户ID
	accountId, err := iam.GetAccountId(ctx)
	if err != nil {
		return err
	}

	// 获取区域信息
	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return csmErr.NewMissingParametersException("region is required")
	}
	// 构造查询参数
	mrp := meta.NewCsmMeshRequestParams()
	mrp.PageNo = int64(pageNo)
	mrp.PageSize = int64(pageSize)
	mrp.OrderBy = "create_time" // 数据库字段名
	if orderBy == "name" {
		mrp.OrderBy = "gateway_name" // 数据库字段名
	}
	mrp.Order = order
	mrp.Keyword = keyword
	if keyword != "" {
		mrp.KeywordType = "gatewayName" // 默认按名称搜索
	}
	mrp.AccountID = accountId
	mrp.Region = region

	// 设置状态筛选条件
	if status != "" {
		mrp.Status = status
	}

	// 获取 srcProduct 参数
	srcProduct := ctx.QueryParam("srcProduct")
	ctx.CsmLogger().Infof("查询实例列表，srcProduct=%s", srcProduct)

	// 调用服务层方法获取分页数据
	gatewayInstances, totalCount, err := core.aiIngressService.GetIngressInstancesWithPagination(ctx, mrp)
	if err != nil {
		ctx.CsmLogger().Errorf("get all ingress instances error: %v", err)
		return err
	}

	// 根据 srcProduct 进行过滤，只有 srcProduct 不是 aibox 的实例才会返回
	var filteredInstances []meta.AiGateway
	for _, instance := range gatewayInstances {
		// 需要从数据库中获取实例的 srcProduct 信息进行过滤
		gatewayInfo, err := core.aigatewayModel.GetAIGatewayInfo(ctx, instance.IngressId, instance.IngressId)
		if err != nil {
			ctx.CsmLogger().Warnf("failed to get gateway info for instance %s: %v", instance.IngressId, err)
			continue
		}
		if gatewayInfo != nil && *gatewayInfo != nil {
			gateway := **gatewayInfo
			// 只返回 srcProduct 不是 aibox 的实例
			if gateway.SrcProduct != constants.AIGatewayProductAibox {
				filteredInstances = append(filteredInstances, instance)
			} else {
				ctx.CsmLogger().Infof("过滤掉aibox实例: %s", instance.IngressId)
			}
		}
	}

	// 更新总数和结果
	gatewayInstances = filteredInstances
	totalCount = int64(len(filteredInstances))

	// 构建响应
	pageResponse := struct {
		OrderBy    string           `json:"orderBy"`
		Order      string           `json:"order"`
		PageNo     int              `json:"pageNo"`
		PageSize   int              `json:"pageSize"`
		TotalCount int64            `json:"totalCount"`
		Result     []meta.AiGateway `json:"result"`
	}{
		OrderBy:    orderBy,
		Order:      order,
		PageNo:     pageNo,
		PageSize:   pageSize,
		TotalCount: totalCount,
		Result:     gatewayInstances,
	}

	// 确保Result不为nil
	if gatewayInstances == nil {
		pageResponse.Result = []meta.AiGateway{}
	}

	return ctx.JSON(http.StatusOK, pageResponse)
}

// GetAIGatewayClusterList 获取AI网关实例关联的集群列表
func (core *APIServerCore) GetAIGatewayClusterList(ctx csmContext.CsmContext) (error error) {
	// 调用aiIngressService的GetAIGatewayClusterList方法获取集群列表
	response, err := core.aiIngressService.GetAIGatewayClusterList(ctx)
	if err != nil {
		return err
	}

	// 返回集群列表响应
	return ctx.JSON(http.StatusOK, response.Page)
}

// AddServiceToGateway 向网关实例添加服务
func (core *APIServerCore) AddServiceToGateway(ctx csmContext.CsmContext) error {
	// 获取路径参数 - 实例ID
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}

	// 绑定请求参数
	addServiceReq := &meta.AddServiceRequest{}
	if err := ctx.Bind(addServiceReq); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 参数校验
	if len(addServiceReq.ServiceList) == 0 {
		return csmErr.NewInvalidParameterValueException("serviceList不能为空")
	}

	// 判断服务来源是否支持
	if addServiceReq.ServiceSource != "CCE" {
		return csmErr.NewInvalidParameterValueException("目前仅支持CCE服务来源")
	}
	region := ctx.Get(reg.ContextRegion).(string)
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	// 调用服务层添加服务
	response, err := core.aiServiceService.AddServices(ctx, instanceId, addServiceReq, hostedClusterId)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to add services: %v", err)
		return err
	}

	return ctx.JSON(http.StatusOK, response.Result)
}

// GetServicesByInstanceID 根据实例ID查询服务列表
func (core *APIServerCore) GetServicesByInstanceID(ctx csmContext.CsmContext) error {
	// 获取路径参数 - 实例ID
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	// 获取查询参数
	keyword := ctx.QueryParam("keyword")

	// 获取分页参数
	pageNo := 1
	pageSize := 10
	orderBy := "createTime"
	order := "desc"

	if ctx.QueryParam("pageNo") != "" {
		pageNoInt, err := strconv.Atoi(ctx.QueryParam("pageNo"))
		if err == nil && pageNoInt > 0 {
			pageNo = pageNoInt
		}
	}

	if ctx.QueryParam("pageSize") != "" {
		pageSizeInt, err := strconv.Atoi(ctx.QueryParam("pageSize"))
		if err == nil && pageSizeInt > 0 {
			pageSize = pageSizeInt
		}
	}

	if ctx.QueryParam("orderBy") != "" {
		orderBy = ctx.QueryParam("orderBy")
	}

	if ctx.QueryParam("order") != "" {
		orderStr := ctx.QueryParam("order")
		if orderStr == "asc" || orderStr == "desc" {
			order = orderStr
		}
	}

	// 调用服务层获取列表
	region := ctx.Get(reg.ContextRegion).(string)
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	response, err := core.aiServiceService.GetServicesList(ctx, instanceId, keyword, pageNo, pageSize, orderBy, order, hostedClusterId)
	if err != nil {
		ctx.CsmLogger().Errorf("Get services list error: %v", err)
		return err
	}

	return ctx.JSON(http.StatusOK, response.Page)
}

func (core *APIServerCore) GetServicesByServicesource(ctx csmContext.CsmContext) error {
	// 获取路径参数 - 实例ID
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}
	// 获取查询参数
	serviceSource := ctx.QueryParam("serviceSource")

	// 调用服务层获取列表 - 注意：keyword参数在 ListServices 方法中被用作 ServiceName 的搜索条件
	// 因此传空字符串，后面会手动过滤 ServiceSource
	region := ctx.Get(reg.ContextRegion).(string)
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	response, err := core.aiServiceService.GetServicesList(ctx, instanceId, "", 1, 1000, "", "", hostedClusterId)
	if err != nil {
		ctx.CsmLogger().Errorf("Get services list error: %v", err)
		return err
	}

	// 将原始响应重新格式化为请求的简化结构
	type SimpleService struct {
		ServiceName string `json:"serviceName"`
		Namespace   string `json:"namespace"`
		ClusterId   string `json:"clusterId"`
	}

	var formattedResponse []SimpleService

	// 从response.Page.Result提取服务名称和命名空间
	for _, service := range response.Page.Result {
		// 只有当 ServiceSource 匹配或未指定时才添加到结果中
		if serviceSource == "" || service.ServiceSource == serviceSource {
			formattedResponse = append(formattedResponse, SimpleService{
				ServiceName: service.ServiceName,
				Namespace:   service.Namespace,
				ClusterId:   service.ClusterID,
			})
		}
	}

	return ctx.JSON(http.StatusOK, formattedResponse)
}

// GetServiceDetail 根据实例ID和服务名称查询服务详情
func (core *APIServerCore) GetServiceDetail(ctx csmContext.CsmContext) error {
	// 获取路径参数 - 实例ID和服务名称
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	serviceName := ctx.Param("serviceName")
	if serviceName == "" {
		return csmErr.NewMissingParametersException("serviceName")
	}

	// 调用服务层获取详情
	response, err := core.aiServiceService.GetServiceDetail(ctx, instanceId, serviceName)
	if err != nil {
		ctx.CsmLogger().Errorf("Get service detail error: %v", err)
		return err
	}

	return ctx.JSON(http.StatusOK, response.Result)
}

// DeleteService 根据实例ID和服务名称删除服务
func (core *APIServerCore) DeleteService(ctx csmContext.CsmContext) error {
	// 获取路径参数 - 实例ID和服务名称
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	serviceName := ctx.Param("serviceName")
	if serviceName == "" {
		return csmErr.NewMissingParametersException("serviceName")
	}

	namespace := ctx.Param("namespace")
	if namespace == "" {
		return csmErr.NewMissingParametersException("namespace")
	}

	// 获取区域
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取目标网关的命名空间
	gatewayNamespace := "istio-system-" + instanceId
	clusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(clusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	// 创建集群客户端
	client, err := core.cceService.NewClient(ctx, region, clusterId, meta.HostingMeshType)
	if err != nil {
		return errors.Wrap(err, "Failed to create Istio client")
	}
	istioClient := client.Istio()

	// 查询VirtualService资源列表
	vsList, err := istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return errors.Wrap(err, "Failed to list VirtualServices")
	}

	// 检查是否有VirtualService引用了当前服务
	serviceHostPattern := serviceName + "." // 匹配格式如: serviceName.namespace.svc.cluster.local
	for _, vs := range vsList.Items {
		for _, httpRoute := range vs.Spec.Http {
			for _, route := range httpRoute.Route {
				if route.Destination != nil && route.Destination.Host != "" {
					// 检查目标主机名是否引用了当前服务
					if strings.HasPrefix(route.Destination.Host, serviceHostPattern) {
						//return csmErr.NewInvalidParameterValueException("当前服务被路由规则引用，无法删除。请先删除相关路由：" + vs.Name)
						return errors.New("当前服务被路由规则引用，无法删除。请先删除相关路由：" + vs.Name)
					}
				}
			}
		}
	}

	// 调用服务层删除服务
	err = core.aiServiceService.DeleteService(ctx, instanceId, serviceName, namespace)
	if err != nil {
		ctx.CsmLogger().Errorf("Delete service error: %v", err)
		return err
	}

	return ctx.JSON(http.StatusOK, nil)
}

// GetClusterNamespaces 根据集群ID查询命名空间列表
func (core *APIServerCore) GetClusterNamespaces(ctx csmContext.CsmContext) error {
	// 获取查询参数 - 集群ID
	clusterId := ctx.QueryParam("clusterId")
	if clusterId == "" {
		return csmErr.NewMissingParametersException("clusterId")
	}

	// 获取区域
	region := ctx.Get(reg.ContextRegion).(string)

	// 创建k8s客户端
	client, err := core.cceService.NewClient(ctx, region, clusterId, meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to create cluster client: %v", err)
		return csmErr.NewDBOperationException(err)
	}

	// 获取命名空间列表
	allNamespaces, err := client.Kube().CoreV1().Namespaces().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to list namespaces: %v", err)
		return csmErr.NewDBOperationException(err)
	}

	// 提取命名空间名称
	namespaceNames := make([]string, 0, len(allNamespaces.Items))
	for _, ns := range allNamespaces.Items {
		namespaceNames = append(namespaceNames, ns.Name)
	}

	// 返回成功响应
	response := struct {
		Status  int         `json:"status"`
		Message string      `json:"message"`
		Result  interface{} `json:"result"`
	}{
		Status:  http.StatusOK,
		Message: "success",
		Result:  namespaceNames,
	}

	// 确保namespaceNames不为nil
	if namespaceNames == nil {
		response.Result = []string{}
	}

	return ctx.JSON(http.StatusOK, response.Result)
}

// GetClusterServices 根据集群ID和命名空间查询服务列表
func (core *APIServerCore) GetClusterServices(ctx csmContext.CsmContext) error {
	// 获取查询参数 - 集群ID和命名空间
	clusterId := ctx.QueryParam("clusterId")
	if clusterId == "" {
		return csmErr.NewMissingParametersException("clusterId")
	}

	namespace := ctx.QueryParam("namespace")
	if namespace == "" {
		return csmErr.NewMissingParametersException("namespace")
	}

	// 获取区域
	region := ctx.Get(reg.ContextRegion).(string)

	// 创建k8s客户端
	client, err := core.cceService.NewClient(ctx, region, clusterId, meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to create cluster client: %v", err)
		return csmErr.NewDBOperationException(err)
	}

	// 获取服务列表
	allServices, err := client.Kube().CoreV1().Services(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to list services in namespace %s: %v", namespace, err)
		return csmErr.NewDBOperationException(err)
	}

	// 提取服务名称
	serviceNames := make([]string, 0, len(allServices.Items))
	for _, svc := range allServices.Items {
		serviceNames = append(serviceNames, svc.Name)
	}

	// 返回成功响应
	response := struct {
		Status  int         `json:"status"`
		Message string      `json:"message"`
		Result  interface{} `json:"result"`
	}{
		Status:  http.StatusOK,
		Message: "success",
		Result:  serviceNames,
	}

	// 确保serviceNames不为nil
	if serviceNames == nil {
		response.Result = []string{}
	}

	return ctx.JSON(http.StatusOK, response.Result)
}

// CreateRoute 创建API网关路由
func (core *APIServerCore) CreateRoute(ctx csmContext.CsmContext) error {
	ctx.CsmLogger().Infof("开始创建路由")

	// 获取路径参数
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	clusterId := ctx.Param("clusterId")
	if clusterId == "" {
		return csmErr.NewMissingParametersException("clusterId")
	}

	// 获取请求头中的区域信息
	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return csmErr.NewMissingParametersException("X-Region header")
	}

	// 绑定请求参数
	routeRequest := &meta.AIRouteRequest{}
	if err := ctx.Bind(routeRequest); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	ctx.CsmLogger().Infof("路由请求参数: routeName=%s, multiService=%v", routeRequest.RouteName, routeRequest.MultiService)
	srcProduct := routeRequest.SrcProduct
	// 验证多服务配置
	if err := core.validateMultiServiceConfig(routeRequest); err != nil {
		return err
	}

	// 验证Token限流配置
	if err := core.validateTokenRateLimitConfig(routeRequest); err != nil {
		return err
	}

	// 验证Rewrite配置
	rewriteConfig := routeRequest.Rewrite
	ctx.CsmLogger().Infof("验证路径重写配置: enabled=%v, path=%s",
		rewriteConfig.Enabled, rewriteConfig.Path)
	if err := routeRequest.ValidateRewriteConfig(); err != nil {
		ctx.CsmLogger().Errorf("路径重写配置验证失败: %v", err)
		return csmErr.NewInvalidParameterValueException(err.Error())
	}

	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	// 获取目标网关的命名空间
	gatewayNamespace := "istio-system-" + instanceId

	// 创建Istio客户端
	client, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		return errors.Wrap(err, "Failed to create Istio client")
	}
	istioClient := client.Istio()

	ctx.CsmLogger().Infof("开始创建VirtualService")

	// 根据是否为多服务模式创建不同的VirtualService
	var virtualService *v1alpha3.VirtualService
	if routeRequest.MultiService {
		virtualService, err = core.createMultiServiceVirtualService(ctx, routeRequest, gatewayNamespace)
	} else {
		virtualService, err = core.createSingleServiceVirtualService(ctx, routeRequest, gatewayNamespace)
	}

	if err != nil {
		return errors.Wrap(err, "Failed to create VirtualService configuration")
	}

	// 创建VirtualService资源
	createdVS, err := istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).Create(
		context.TODO(), virtualService, metav1.CreateOptions{})
	if err != nil && !kubeErrors.IsAlreadyExists(err) {
		return errors.Wrap(err, "Failed to create VirtualService")
	}

	ctx.CsmLogger().Infof("VirtualService创建成功: %s", createdVS.Name)

	// 创建DestinationRule（如果需要）
	if err := core.createDestinationRulesIfNeeded(ctx, routeRequest, gatewayNamespace, istioClient); err != nil {
		ctx.CsmLogger().Errorf("创建DestinationRule失败: %v", err)
		// 继续执行，不影响主流程
	}

	// 创建模型路由EnvoyFilter（如果是按模型名称分发）
	if routeRequest.MultiService && routeRequest.TrafficDistributionStrategy == "model_name" {
		if err := core.createModelRouteEnvoyFilter(ctx, routeRequest, gatewayNamespace, client); err != nil {
			ctx.CsmLogger().Errorf("创建模型路由EnvoyFilter失败: %v", err)
			// 继续执行，不影响主流程
		}
	}

	// 如果启用了Token限流，创建Token限流插件
	tokenRateLimitResponse := meta.TokenRateLimitResponse{}

	if routeRequest.TokenRateLimit.Enabled {
		ctx.CsmLogger().Infof("Token rate limit is enabled for route %s, creating plugin", routeRequest.RouteName)
		err = core.createTokenRateLimitPlugin(ctx, gatewayNamespace,
			routeRequest.RouteName, &routeRequest.TokenRateLimit, client)
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to create token rate limit plugin: %v", err)
			// 继续执行，不影响主流程
		} else {
			// 准备响应数据
			ruleName := fmt.Sprintf("rate-limit-%s", strings.ToLower(routeRequest.RouteName))
			tokenRateLimitResponse.RuleName = ruleName
			tokenRateLimitResponse.Enabled = routeRequest.TokenRateLimit.Enabled
			tokenRateLimitResponse.RuleItems = routeRequest.TokenRateLimit.RuleItems
		}
	}

	// 当srcProduct=pom时，将传入的service添加到数据库中
	//if srcProduct == "pom" {
	//	ctx.CsmLogger().Infof("srcProduct为pom，开始添加服务到数据库")
	//
	//	// 构建服务列表和命名空间信息
	//	var serviceList []string
	//	var namespace string
	//
	//	if routeRequest.MultiService {
	//		// 多服务模式
	//		multiServices, err := routeRequest.GetMultiTargetServices()
	//		if err != nil {
	//			ctx.CsmLogger().Errorf("获取多服务配置失败: %v", err)
	//		} else {
	//			for _, service := range multiServices {
	//				serviceList = append(serviceList, service.ServiceName)
	//				// 假设所有服务在同一个命名空间中，使用第一个服务的命名空间
	//				if namespace == "" {
	//					namespace = service.Namespace
	//				}
	//			}
	//		}
	//	} else {
	//		// 单服务模式
	//		singleService, err := routeRequest.GetSingleTargetService()
	//		if err != nil {
	//			ctx.CsmLogger().Errorf("获取单服务配置失败: %v", err)
	//		} else {
	//			serviceList = append(serviceList, singleService.ServiceName)
	//			namespace = singleService.Namespace
	//		}
	//	}
	//
	//	if len(serviceList) > 0 && namespace != "" {
	//		addServiceRequest := &meta.AddServiceRequest{
	//			ClusterID:     clusterId,
	//			ServiceSource: "CCE",
	//			Namespace:     namespace,
	//			ServiceList:   serviceList,
	//		}
	//
	//		// 调用AddServices方法
	//		_, err := core.aiServiceService.AddServices(ctx, instanceId, addServiceRequest, hostedClusterId)
	//		if err != nil {
	//			ctx.CsmLogger().Errorf("添加服务到数据库失败: %v", err)
	//			// 不影响主流程，继续执行
	//		} else {
	//			ctx.CsmLogger().Infof("成功添加%d个服务到数据库", len(serviceList))
	//		}
	//	}
	//}

	if srcProduct != "aibox" {
		// 获取动态客户端
		dynamicClient := client.Dynamic()

		// 无论是否启用认证，都处理key-auth插件，记录路由规则
		groupVersionResource := schema.GroupVersionResource{
			Group:    "extensions.higress.io",
			Version:  "v1alpha1",
			Resource: "wasmplugins",
		}
		unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to list WasmPlugins when updating consumers: %v", err)
			// 继续执行，不影响主流程
		} else {
			// 查找key-auth插件
			var keyAuthPlugin *unstructured.Unstructured
			for _, item := range unstructuredList.Items {
				name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
				if name == constants.GetKeyAuthPluginName() {
					keyAuthPlugin = &item
					break
				}
			}

			// 如果没有找到key-auth插件，创建基础插件并添加路由规则
			if keyAuthPlugin == nil {
				ctx.CsmLogger().Infof("Key Auth插件不存在，正在创建基础插件并添加路由规则")

				// 创建基础Key Auth插件
				err := core.createBaseKeyAuthPlugin(ctx, gatewayNamespace, client)
				if err != nil {
					ctx.CsmLogger().Errorf("创建基础Key Auth插件失败: %v", err)
					// 继续执行，不影响主流程
				} else {
					// 重新查询插件
					unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})
					if err == nil {
						for _, item := range unstructuredList.Items {
							name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
							if name == constants.GetKeyAuthPluginName() {
								keyAuthPlugin = &item
								break
							}
						}
					}
				}
			}

			if keyAuthPlugin != nil {
				// 获取现有规则
				spec, exists, _ := unstructured.NestedMap(keyAuthPlugin.Object, "spec")
				if !exists {
					ctx.CsmLogger().Errorf("Failed to get spec from key-auth plugin")
					// 跳过此步骤，继续主流程
				} else {
					matchRules, exists, _ := unstructured.NestedSlice(spec, "matchRules")
					if !exists {
						matchRules = []interface{}{}
					}

					// 创建路由ID到规则的映射
					routeRulesMap := make(map[string]map[string]interface{})

					// 收集现有规则
					for _, ruleItem := range matchRules {
						rule, ok := ruleItem.(map[string]interface{})
						if !ok {
							continue
						}

						ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
						if !exists || len(ingressList) == 0 {
							continue
						}

						// 假设每个规则只对应一个路由
						for _, ingressItem := range ingressList {
							if routeName, ok := ingressItem.(string); ok {
								routeRulesMap[routeName] = rule
								break
							}
						}
					}

					// 检查当前路由是否已有规则
					var newRules []interface{}

					// 获取所有未关联当前路由的规则
					for _, rule := range matchRules {
						ruleMap, ok := rule.(map[string]interface{})
						if !ok {
							continue
						}

						ingressList, exists, _ := unstructured.NestedSlice(ruleMap, "ingress")
						if !exists {
							newRules = append(newRules, rule)
							continue
						}

						// 检查规则是否关联当前路由
						containsRoute := false
						for _, ingress := range ingressList {
							if route, ok := ingress.(string); ok && route == routeRequest.RouteName {
								containsRoute = true
								break
							}
						}

						if !containsRoute {
							newRules = append(newRules, rule)
						}
					}

					// 为每个路由创建一个matchRules记录，无论是否启用认证
					// 处理消费者列表 - 不管是否开启认证都要确保allow数组不为空
					allowList := []interface{}{}
					if routeRequest.AuthEnabled && len(routeRequest.AllowedConsumers) > 0 {
						// 开启认证且有消费者时，添加所有消费者
						for _, consumerName := range routeRequest.AllowedConsumers {
							allowList = append(allowList, consumerName)
						}
					} else {
						// 未开启认证 或 开启认证但没有消费者时，都添加虚拟消费者
						allowList = core.addVirtualConsumerToEmptyAllowArray(allowList)
					}

					routeRule := map[string]interface{}{
						"config": map[string]interface{}{
							"allow": allowList,
						},
						"configDisable": !routeRequest.AuthEnabled, // 如果AuthEnabled为false，则configDisable为true
						"ingress":       []interface{}{routeRequest.RouteName},
					}

					newRules = append(newRules, routeRule)

					// 更新规则
					if err := unstructured.SetNestedSlice(keyAuthPlugin.Object, newRules, "spec", "matchRules"); err != nil {
						ctx.CsmLogger().Errorf("Failed to update matchRules: %v", err)
					} else {
						// 更新key-auth插件
						_, err = dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).Update(
							context.TODO(), keyAuthPlugin, metav1.UpdateOptions{})
						if err != nil {
							ctx.CsmLogger().Errorf("Failed to update key-auth plugin: %v", err)
							// 继续执行，不影响主流程
						}
					}
				}
			}
		}

	}

	// 处理AI配额插件的ingress列表
	if routeRequest.AuthEnabled {
		err = core.updateAIQuotaPluginIngress(ctx, gatewayNamespace, routeRequest.RouteName, "add", client)
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to update AI Quota plugin ingress: %v", err)
			// 继续执行，不影响主流程
		}
	}

	// 构建响应对象
	var serviceName string
	var targetServiceResponse interface{}

	if routeRequest.MultiService {
		serviceName = "multi-service"
		services, _ := routeRequest.GetMultiTargetServices()
		targetServiceResponse = services
	} else {
		service, _ := routeRequest.GetSingleTargetService()
		serviceName = service.ServiceName
		targetServiceResponse = service
	}

	// 构建响应用的 MatchRules
	responseMatchRules := routeRequest.MatchRules

	// 获取正确的重写配置
	responseRewriteConfig := routeRequest.Rewrite
	ctx.CsmLogger().Infof("响应构建：rewrite配置 enabled=%v, path=%s",
		responseRewriteConfig.Enabled, responseRewriteConfig.Path)

	response := &meta.AIRouteResponse{
		RouteName:                   routeRequest.RouteName,
		MatchPath:                   routeRequest.MatchRules.PathRule.Value,
		MatchRules:                  &responseMatchRules, // 匹配规则，不包含rewrite配置
		ServiceName:                 serviceName,
		CreateTime:                  time.Now().Format("2006-01-02 15:04:05"),
		MultiService:                routeRequest.MultiService,
		TrafficDistributionStrategy: routeRequest.TrafficDistributionStrategy,
		Rewrite:                     responseRewriteConfig, // rewrite配置在顶级字段
		TargetService:               targetServiceResponse,
		AuthEnabled:                 routeRequest.AuthEnabled,
		AllowedConsumers:            routeRequest.AllowedConsumers,
		TokenRateLimit:              tokenRateLimitResponse,
		TimeoutPolicy:               routeRequest.TimeoutPolicy,
		RetryPolicy:                 routeRequest.RetryPolicy,
	}

	ctx.CsmLogger().Infof("路由创建成功: %s", routeRequest.RouteName)
	// 返回成功响应
	return ctx.JSON(http.StatusOK, response)
}

// ListIPRestrictions 查看IP黑白名单列表
func (core *APIServerCore) ListIPRestrictions(ctx csmContext.CsmContext) error {
	// 获取实例ID
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	ctx.CsmLogger().Infof("开始查询IP黑白名单列表，实例ID: %s", instanceId)

	region := ctx.Get(reg.ContextRegion).(string)
	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 获取查询参数
	nameFilter := ctx.QueryParam("name")

	// 使用dynamic client获取WasmPlugin资源，使用标签选择器过滤
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	// 使用标签选择器直接筛选IP黑白名单插件
	dynamicClient := hostingClient.Dynamic()
	unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: constants.GetIPRestrictionPluginLabelSelector(),
	})
	if err != nil {
		ctx.CsmLogger().Errorf("查询IP黑白名单WasmPlugins失败: %v", err)
		return err
	}

	// 处理IP黑白名单插件列表
	var ipRestrictionItems []meta.IPRestrictionItem
	for _, item := range unstructuredList.Items {
		name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")

		// 从annotations中获取规则信息
		annotations, exists, _ := unstructured.NestedStringMap(item.Object, "metadata", "annotations")
		if !exists {
			continue
		}

		ruleName := annotations["aigw.rule.name"]
		ruleType := annotations["aigw.rule.type"]
		scope := annotations["aigw.rule.scope"]

		// 应用名称过滤（模糊查询）
		if nameFilter != "" && !strings.Contains(strings.ToLower(ruleName), strings.ToLower(nameFilter)) {
			continue
		}

		// 获取启用状态
		defaultConfigDisable, _, _ := unstructured.NestedBool(item.Object, "spec", "defaultConfigDisable")
		enabled := !defaultConfigDisable

		// 获取IP地址列表
		var ipAddresses []string
		defaultConfig, exists, _ := unstructured.NestedMap(item.Object, "spec", "defaultConfig")
		if exists {
			if ruleType == "blacklist" {
				if denyList, exists, _ := unstructured.NestedSlice(defaultConfig, "deny"); exists {
					for _, ip := range denyList {
						if ipStr, ok := ip.(string); ok {
							ipAddresses = append(ipAddresses, ipStr)
						}
					}
				}
			} else if ruleType == "whitelist" {
				if allowList, exists, _ := unstructured.NestedSlice(defaultConfig, "allow"); exists {
					for _, ip := range allowList {
						if ipStr, ok := ip.(string); ok {
							ipAddresses = append(ipAddresses, ipStr)
						}
					}
				}
			}
		}

		ipRestrictionItems = append(ipRestrictionItems, meta.IPRestrictionItem{
			ID:          name,
			Name:        ruleName,
			Enabled:     enabled,
			Type:        ruleType,
			Scope:       scope,
			IPAddresses: ipAddresses,
		})
	}
	// 返回成功响应
	return ctx.JSON(http.StatusOK, ipRestrictionItems)
}

// GetIPRestrictionDetail 查看IP黑白名单详情
func (core *APIServerCore) GetIPRestrictionDetail(ctx csmContext.CsmContext) error {
	// 获取参数
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	id := ctx.Param("id")
	if id == "" {
		return csmErr.NewMissingParametersException("id")
	}

	ctx.CsmLogger().Infof("开始查询IP黑白名单详情，实例ID: %s, 规则ID: %s", instanceId, id)

	region := ctx.Get(reg.ContextRegion).(string)
	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 使用dynamic client获取WasmPlugin资源
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := hostingClient.Dynamic()
	plugin, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).Get(context.TODO(), id, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return csmErr.NewResourceNotFoundException("IP黑白名单不存在", err)
		}
		ctx.CsmLogger().Errorf("查询WasmPlugin失败: %v", err)
		return err
	}

	// 从annotations中获取规则信息
	annotations, exists, _ := unstructured.NestedStringMap(plugin.Object, "metadata", "annotations")
	if !exists {
		return csmErr.NewInvalidParameterValueException("插件annotations信息缺失")
	}

	ruleName := annotations["aigw.rule.name"]
	ruleDescription := annotations["aigw.rule.description"]
	ruleType := annotations["aigw.rule.type"]
	scope := annotations["aigw.rule.scope"]

	// 获取启用状态
	defaultConfigDisable, _, _ := unstructured.NestedBool(plugin.Object, "spec", "defaultConfigDisable")
	enabled := !defaultConfigDisable

	// 获取IP地址列表
	var ipAddresses []string
	defaultConfig, exists, _ := unstructured.NestedMap(plugin.Object, "spec", "defaultConfig")
	if exists {
		if ruleType == "blacklist" {
			if denyList, exists, _ := unstructured.NestedSlice(defaultConfig, "deny"); exists {
				for _, ip := range denyList {
					if ipStr, ok := ip.(string); ok {
						ipAddresses = append(ipAddresses, ipStr)
					}
				}
			}
		} else if ruleType == "whitelist" {
			if allowList, exists, _ := unstructured.NestedSlice(defaultConfig, "allow"); exists {
				for _, ip := range allowList {
					if ipStr, ok := ip.(string); ok {
						ipAddresses = append(ipAddresses, ipStr)
					}
				}
			}
		}
	}

	// 获取创建时间和更新时间
	createTime := ""
	updateTime := ""
	if creationTimestamp, exists, _ := unstructured.NestedString(plugin.Object, "metadata", "creationTimestamp"); exists {
		if t, err := time.Parse(time.RFC3339, creationTimestamp); err == nil {
			createTime = t.In(time.FixedZone("CST", 8*60*60)).Format("2006-01-02 15:04:05")
		}
	}

	// 对于Kubernetes资源，我们可以从creationTimestamp获取时间，由于没有专门的updateTime字段，
	// 这里暂时使用创建时间作为更新时间
	updateTime = createTime

	response := meta.IPRestrictionInfo{
		ID:          id,
		Enabled:     enabled,
		Name:        ruleName,
		Description: ruleDescription,
		Type:        ruleType,
		Scope:       scope,
		IPAddresses: ipAddresses,
		CreateTime:  createTime,
		UpdateTime:  updateTime,
	}

	ctx.CsmLogger().Infof("成功查询IP黑白名单详情: %s", id)

	// 返回成功响应
	return ctx.JSON(http.StatusOK, response)
}

// UpdateIPRestriction 编辑IP黑白名单
func (core *APIServerCore) UpdateIPRestriction(ctx csmContext.CsmContext) error {
	// 获取参数
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	id := ctx.Param("id")
	if id == "" {
		return csmErr.NewMissingParametersException("id")
	}

	ctx.CsmLogger().Infof("开始编辑IP黑白名单，实例ID: %s, 规则ID: %s", instanceId, id)

	// 绑定请求参数
	updateIPRestrictionReq := &meta.UpdateIPRestrictionRequest{}
	if err := ctx.Bind(updateIPRestrictionReq); err != nil {
		ctx.CsmLogger().Errorf("绑定请求参数失败: %v", err)
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 验证请求参数
	if err := updateIPRestrictionReq.Validate(); err != nil {
		ctx.CsmLogger().Errorf("参数验证失败: %v", err)
		return csmErr.NewInvalidParameterValueException(err.Error())
	}

	region := ctx.Get(reg.ContextRegion).(string)
	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 使用dynamic client删除WasmPlugin资源
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := hostingClient.Dynamic()

	// 先检查资源是否存在
	_, err = dynamicClient.Resource(groupVersionResource).Namespace(namespace).Get(context.TODO(), id, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return csmErr.NewResourceNotFoundException("IP黑白名单不存在", err)
		}
		ctx.CsmLogger().Errorf("查询WasmPlugin失败: %v", err)
		return err
	}

	// 删除资源
	err = dynamicClient.Resource(groupVersionResource).Namespace(namespace).Delete(context.TODO(), id, metav1.DeleteOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("删除IP黑白名单资源失败: %v", err)
		return errors.Wrap(err, "failed to delete IP restriction resources")
	}

	ipType := updateIPRestrictionReq.Type
	pluginName := fmt.Sprintf("ip-restriction-%s", ipType)

	// 准备更新的模板数据
	templateData := meta.IPRestrictionTemplateData{
		PluginName:      pluginName,
		Namespace:       namespace,
		RuleName:        updateIPRestrictionReq.Name, // 保持规则名称不变
		RuleDescription: updateIPRestrictionReq.Description,
		Type:            ipType,
		Scope:           updateIPRestrictionReq.Scope,
		IPAddresses:     updateIPRestrictionReq.IPAddresses,
		IPSourceType:    constants.IPSourceType,
		Enabled:         updateIPRestrictionReq.Enabled,
		PluginURL:       constants.GetIpRestrictionPluginURL(),
	}

	// 根据类型设置状态码和消息
	if updateIPRestrictionReq.Type == "blacklist" {
		templateData.Status = 403
		templateData.Message = "Your IP address is blocked By AI Gateway"
	} else {
		templateData.Status = 451
		templateData.Message = "Your IP address is not in whitelist By AI Gateway"
	}

	ctx.CsmLogger().Infof("模板数据准备完成，类型: %s，IP地址数量: %d", templateData.Type, len(templateData.IPAddresses))

	// 读取模板文件
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("获取当前目录失败: %v", err)
		return errors.Wrap(err, "failed to get current directory")
	}

	templatePath := path.Join(pwd, constants.GetIpRestrictionTemplatePath())
	templateContent, err := os.ReadFile(templatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("读取模板文件失败: %v", err)
		return errors.Wrap(err, "failed to read IP restriction template file")
	}

	// 渲染模板
	tmpl, err := template.New("ip-restriction").Parse(string(templateContent))
	if err != nil {
		ctx.CsmLogger().Errorf("解析模板失败: %v", err)
		return errors.Wrap(err, "failed to parse template")
	}

	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, templateData); err != nil {
		ctx.CsmLogger().Errorf("渲染模板失败: %v", err)
		return errors.Wrap(err, "failed to render template")
	}

	ctx.CsmLogger().Infof("模板渲染完成，开始更新WasmPlugin资源")

	// 更新资源
	yamlObjects := []string{rendered.String()}
	err = kube.CreateResources(ctx, hostingClient, yamlObjects)
	if err != nil {
		ctx.CsmLogger().Errorf("更新IP黑白名单资源失败: %v", err)
		return errors.Wrap(err, "failed to update IP restriction resources")
	}

	ctx.CsmLogger().Infof("成功更新IP黑白名单插件: %s", id)

	response := meta.IPRestrictionInfo{
		ID:          id,
		Enabled:     updateIPRestrictionReq.Enabled,
		Name:        updateIPRestrictionReq.Name,
		Description: updateIPRestrictionReq.Description,
		Type:        updateIPRestrictionReq.Type,
		Scope:       updateIPRestrictionReq.Scope,
		IPAddresses: updateIPRestrictionReq.IPAddresses,
	}

	ctx.CsmLogger().Infof("IP黑白名单编辑完成，返回响应数据")

	// 返回成功响应
	return ctx.JSON(http.StatusOK, response)
}

// DeleteIPRestriction 删除IP黑白名单
func (core *APIServerCore) DeleteIPRestriction(ctx csmContext.CsmContext) error {
	// 获取参数
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	id := ctx.Param("id")
	if id == "" {
		return csmErr.NewMissingParametersException("id")
	}

	ctx.CsmLogger().Infof("开始删除IP黑白名单，实例ID: %s, 规则ID: %s", instanceId, id)

	region := ctx.Get(reg.ContextRegion).(string)
	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 使用dynamic client删除WasmPlugin资源
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := hostingClient.Dynamic()

	// 先检查资源是否存在
	_, err = dynamicClient.Resource(groupVersionResource).Namespace(namespace).Get(context.TODO(), id, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return csmErr.NewResourceNotFoundException("IP黑白名单不存在", err)
		}
		ctx.CsmLogger().Errorf("查询WasmPlugin失败: %v", err)
		return err
	}

	// 删除资源
	err = dynamicClient.Resource(groupVersionResource).Namespace(namespace).Delete(context.TODO(), id, metav1.DeleteOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("删除IP黑白名单资源失败: %v", err)
		return errors.Wrap(err, "failed to delete IP restriction resources")
	}

	ctx.CsmLogger().Infof("成功删除IP黑白名单插件: %s", id)

	// 返回成功响应
	return ctx.JSON(http.StatusOK, true)
}

// validateMultiServiceConfig 验证多服务配置
func (core *APIServerCore) validateMultiServiceConfig(routeRequest *meta.AIRouteRequest) error {
	if routeRequest.MultiService {
		// 验证流量分发策略
		if routeRequest.TrafficDistributionStrategy == "" {
			return csmErr.NewInvalidParameterValueException("trafficDistributionStrategy is required when multiService is true")
		}

		if routeRequest.TrafficDistributionStrategy != "ratio" && routeRequest.TrafficDistributionStrategy != "model_name" {
			return csmErr.NewInvalidParameterValueException("trafficDistributionStrategy must be 'ratio' or 'model_name'")
		}

		// 获取多服务配置
		services, err := routeRequest.GetMultiTargetServices()
		if err != nil {
			return csmErr.NewInvalidParameterValueException("invalid targetService format for multi-service mode")
		}

		if len(services) == 0 {
			return csmErr.NewInvalidParameterValueException("at least one service is required in multi-service mode")
		}

		// 验证按模型名称分发
		if routeRequest.TrafficDistributionStrategy == "model_name" {
			modelNames := make(map[string]bool)
			for i, service := range services {
				if service.ModelName == "" {
					return csmErr.NewInvalidParameterValueException(fmt.Sprintf("service #%d: modelName is required for model_name strategy", i+1))
				}
				if modelNames[service.ModelName] {
					return csmErr.NewInvalidParameterValueException(fmt.Sprintf("duplicate modelName: %s", service.ModelName))
				}
				modelNames[service.ModelName] = true
			}
		}
	} else {
		// 单服务模式验证
		_, err := routeRequest.GetSingleTargetService()
		if err != nil {
			return csmErr.NewInvalidParameterValueException("invalid targetService format for single-service mode")
		}
	}

	return nil
}

// validateTokenRateLimitConfig 验证Token限流配置
func (core *APIServerCore) validateTokenRateLimitConfig(routeRequest *meta.AIRouteRequest) error {
	if routeRequest.TokenRateLimit.Enabled && len(routeRequest.TokenRateLimit.RuleItems) == 0 {
		return csmErr.NewInvalidParameterValueException("Token rate limit is enabled but no rule items provided")
	}

	// 验证每个规则项的匹配条件
	if routeRequest.TokenRateLimit.Enabled {
		for i, item := range routeRequest.TokenRateLimit.RuleItems {
			// 当类型为header或query_param时，value字段必填
			if (item.MatchCondition.Type == "header" || item.MatchCondition.Type == "query_param") &&
				item.MatchCondition.Value == "" {
				return csmErr.NewInvalidParameterValueException(
					fmt.Sprintf("Rule item #%d: value is required when type is %s",
						i+1, item.MatchCondition.Type))
			}

			// 验证token数量必须大于0
			if item.LimitConfig.TokenAmount <= 0 {
				return csmErr.NewInvalidParameterValueException(
					fmt.Sprintf("Rule item #%d: token amount must be greater than 0", i+1))
			}

			// 验证时间单位必须是有效值
			timeUnit := item.LimitConfig.TimeUnit
			if timeUnit != "second" && timeUnit != "minute" && timeUnit != "hour" && timeUnit != "day" {
				return csmErr.NewInvalidParameterValueException(
					fmt.Sprintf("Rule item #%d: invalid time unit '%s', must be one of: second, minute, hour, day",
						i+1, timeUnit))
			}
		}
	}

	return nil
}

// createSingleServiceVirtualService 创建单服务VirtualService
func (core *APIServerCore) createSingleServiceVirtualService(
	ctx csmContext.CsmContext,
	routeRequest *meta.AIRouteRequest,
	gatewayNamespace string) (*v1alpha3.VirtualService, error) {
	service, err := routeRequest.GetSingleTargetService()
	if err != nil {
		return nil, err
	}

	// 创建匹配请求规则
	httpMatches := []*istionetworkingv1alpha3.HTTPMatchRequest{
		{
			Gateways: []string{
				gatewayNamespace + "/gateway-internal",
			},
			Uri: createUriMatchRequest(
				routeRequest.MatchRules.PathRule.MatchType,
				routeRequest.MatchRules.PathRule.Value,
				routeRequest.MatchRules.PathRule.CaseSensitive,
			),
		},
	}

	// 添加HTTP方法匹配
	if len(routeRequest.MatchRules.Methods) > 0 {
		httpMatches[0].Method = createMethodMatchRequest(routeRequest.MatchRules.Methods)
	}

	// 添加Headers匹配
	if len(routeRequest.MatchRules.Headers) > 0 {
		httpMatches[0].Headers = createHeaderMatchRequests(routeRequest.MatchRules.Headers)
	}

	// 添加QueryParams匹配
	if len(routeRequest.MatchRules.QueryParams) > 0 {
		httpMatches[0].QueryParams = createQueryParamMatchRequests(routeRequest.MatchRules.QueryParams)
	}

	// 构建HTTP路由配置
	httpRoute := &istionetworkingv1alpha3.HTTPRoute{
		Name:  routeRequest.RouteName,
		Match: httpMatches,
		Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
			{
				Destination: &istionetworkingv1alpha3.Destination{
					Host: service.ServiceName + "." + service.Namespace + ".svc.cluster.local",
					Port: &istionetworkingv1alpha3.PortSelector{
						Number: uint32(service.ServicePort),
					},
					Subset: routeRequest.RouteName + "-" + service.ServiceName,
				},
			},
		},
	}

	// 根据请求参数决定是否设置rewrite
	rewriteConfig := routeRequest.Rewrite
	if rewriteConfig.Enabled {
		ctx.CsmLogger().Infof("单服务：启用路径重写，重写到: %s", rewriteConfig.Path)
		httpRoute.Rewrite = &istionetworkingv1alpha3.HTTPRewrite{
			Uri: rewriteConfig.Path,
		}
	} else {
		ctx.CsmLogger().Infof("单服务：未启用路径重写，保持原始路径")
		// 当rewrite为false时，不设置Rewrite字段，保持原始路径
	}

	// 1. 超时策略
	if routeRequest.TimeoutPolicy != nil && routeRequest.TimeoutPolicy.Enabled {
		ctx.CsmLogger().Infof("设置超时策略: %ds", routeRequest.TimeoutPolicy.Timeout)
		httpRoute.Timeout = durationpb.New(time.Duration(routeRequest.TimeoutPolicy.Timeout) * time.Second)
	}
	// 2. 重试策略
	if routeRequest.RetryPolicy != nil && routeRequest.RetryPolicy.Enabled {
		ctx.CsmLogger().Infof("设置重试策略: attempts=%d, retryOn=%s", routeRequest.RetryPolicy.NumRetries, routeRequest.RetryPolicy.RetryConditions)
		httpRoute.Retries = &istionetworkingv1alpha3.HTTPRetry{
			Attempts: int32(routeRequest.RetryPolicy.NumRetries),
			RetryOn:  routeRequest.RetryPolicy.RetryConditions,
		}
	}

	// 创建VirtualService对象
	virtualService := &v1alpha3.VirtualService{
		TypeMeta: metav1.TypeMeta{
			Kind:       "VirtualService",
			APIVersion: "networking.istio.io/v1alpha3",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      routeRequest.RouteName,
			Namespace: gatewayNamespace,
			Annotations: map[string]string{
				"update-time":             time.Now().Format("2006-01-02 15:04:05"),
				"path-case-sensitive":     strconv.FormatBool(routeRequest.MatchRules.PathRule.CaseSensitive),
				"auth.higress.io/enabled": strconv.FormatBool(routeRequest.AuthEnabled),
				"multi-service":           "false",
				"rewrite-enabled":         strconv.FormatBool(rewriteConfig.Enabled), // 新增注解记录rewrite状态
				"src-product":             routeRequest.SrcProduct,                   // 新增srcProduct注解
			},
		},
		Spec: istionetworkingv1alpha3.VirtualService{
			Hosts: []string{"*"},
			Gateways: []string{
				gatewayNamespace + "/gateway-internal",
			},
			ExportTo: []string{"."},
			Http:     []*istionetworkingv1alpha3.HTTPRoute{httpRoute},
		},
	}

	// 如果指定了负载均衡算法，添加到annotations中
	if service.LoadBalanceAlgorithm != "" {
		virtualService.ObjectMeta.Annotations["load-balance-algorithm"] = service.LoadBalanceAlgorithm
	}

	return virtualService, nil
}

// createMultiServiceVirtualService 创建多服务VirtualService
func (core *APIServerCore) createMultiServiceVirtualService(
	ctx csmContext.CsmContext,
	routeRequest *meta.AIRouteRequest,
	gatewayNamespace string) (*v1alpha3.VirtualService, error) {
	services, err := routeRequest.GetMultiTargetServices()
	if err != nil {
		return nil, err
	}

	if routeRequest.TrafficDistributionStrategy == "ratio" {
		return core.createRatioBasedVirtualService(ctx, routeRequest, services, gatewayNamespace)
	} else if routeRequest.TrafficDistributionStrategy == "model_name" {
		return core.createModelNameBasedVirtualService(ctx, routeRequest, services, gatewayNamespace)
	}

	return nil, fmt.Errorf("unsupported traffic distribution strategy: %s", routeRequest.TrafficDistributionStrategy)
}

// createRatioBasedVirtualService 创建按比例分发的VirtualService
func (core *APIServerCore) createRatioBasedVirtualService(
	ctx csmContext.CsmContext,
	routeRequest *meta.AIRouteRequest,
	services []meta.TargetService,
	gatewayNamespace string) (*v1alpha3.VirtualService, error) {
	// 创建匹配请求规则
	httpMatches := []*istionetworkingv1alpha3.HTTPMatchRequest{
		{
			Gateways: []string{
				gatewayNamespace + "/gateway-internal",
			},
			Uri: createUriMatchRequest(
				routeRequest.MatchRules.PathRule.MatchType,
				routeRequest.MatchRules.PathRule.Value,
				routeRequest.MatchRules.PathRule.CaseSensitive,
			),
		},
	}

	// 添加HTTP方法匹配
	if len(routeRequest.MatchRules.Methods) > 0 {
		httpMatches[0].Method = createMethodMatchRequest(routeRequest.MatchRules.Methods)
	}

	// 添加Headers匹配
	if len(routeRequest.MatchRules.Headers) > 0 {
		httpMatches[0].Headers = createHeaderMatchRequests(routeRequest.MatchRules.Headers)
	}

	// 添加QueryParams匹配
	if len(routeRequest.MatchRules.QueryParams) > 0 {
		httpMatches[0].QueryParams = createQueryParamMatchRequests(routeRequest.MatchRules.QueryParams)
	}

	// 创建路由目标
	var routeDestinations []*istionetworkingv1alpha3.HTTPRouteDestination
	for _, service := range services {
		destination := &istionetworkingv1alpha3.HTTPRouteDestination{
			Destination: &istionetworkingv1alpha3.Destination{
				Host: service.ServiceName + "." + service.Namespace + ".svc.cluster.local",
				Port: &istionetworkingv1alpha3.PortSelector{
					Number: uint32(service.ServicePort),
				},
				Subset: routeRequest.RouteName + "-" + service.ServiceName,
			},
			Weight: int32(service.RequestRatio),
		}
		routeDestinations = append(routeDestinations, destination)
	}

	// 构建HTTP路由配置
	httpRoute := &istionetworkingv1alpha3.HTTPRoute{
		Name:  routeRequest.RouteName,
		Match: httpMatches,
		Route: routeDestinations,
	}

	// 根据请求参数决定是否设置rewrite
	rewriteConfig := routeRequest.Rewrite
	if rewriteConfig.Enabled {
		ctx.CsmLogger().Infof("多服务按比例分发：启用路径重写，重写到: %s", rewriteConfig.Path)
		httpRoute.Rewrite = &istionetworkingv1alpha3.HTTPRewrite{
			Uri: rewriteConfig.Path,
		}
	} else {
		ctx.CsmLogger().Infof("多服务按比例分发：未启用路径重写，保持原始路径")
		// 当rewrite为false时，不设置Rewrite字段，保持原始路径
	}

	// 1. 超时策略
	if routeRequest.TimeoutPolicy != nil && routeRequest.TimeoutPolicy.Enabled {
		ctx.CsmLogger().Infof("设置超时策略: %ds", routeRequest.TimeoutPolicy.Timeout)
		httpRoute.Timeout = durationpb.New(time.Duration(routeRequest.TimeoutPolicy.Timeout) * time.Second)
	}
	// 2. 重试策略
	if routeRequest.RetryPolicy != nil && routeRequest.RetryPolicy.Enabled {
		ctx.CsmLogger().Infof("设置重试策略: attempts=%d, retryOn=%s", routeRequest.RetryPolicy.NumRetries, routeRequest.RetryPolicy.RetryConditions)
		httpRoute.Retries = &istionetworkingv1alpha3.HTTPRetry{
			Attempts: int32(routeRequest.RetryPolicy.NumRetries),
			RetryOn:  routeRequest.RetryPolicy.RetryConditions,
		}
	}

	// 创建VirtualService对象
	virtualService := &v1alpha3.VirtualService{
		TypeMeta: metav1.TypeMeta{
			Kind:       "VirtualService",
			APIVersion: "networking.istio.io/v1alpha3",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      routeRequest.RouteName,
			Namespace: gatewayNamespace,
			Annotations: map[string]string{
				"update-time":                   time.Now().Format("2006-01-02 15:04:05"),
				"path-case-sensitive":           strconv.FormatBool(routeRequest.MatchRules.PathRule.CaseSensitive),
				"auth.higress.io/enabled":       strconv.FormatBool(routeRequest.AuthEnabled),
				"multi-service":                 "true",
				"traffic-distribution-strategy": "ratio",
				"rewrite-enabled":               strconv.FormatBool(rewriteConfig.Enabled), // 新增注解记录rewrite状态
				"src-product":                   routeRequest.SrcProduct,                   // 新增srcProduct注解
			},
		},
		Spec: istionetworkingv1alpha3.VirtualService{
			Hosts: []string{"*"},
			Gateways: []string{
				gatewayNamespace + "/gateway-internal",
			},
			ExportTo: []string{"."},
			Http:     []*istionetworkingv1alpha3.HTTPRoute{httpRoute},
		},
	}

	return virtualService, nil
}

// createModelNameBasedVirtualService 创建按模型名称分发的VirtualService
func (core *APIServerCore) createModelNameBasedVirtualService(
	ctx csmContext.CsmContext,
	routeRequest *meta.AIRouteRequest,
	services []meta.TargetService,
	gatewayNamespace string) (*v1alpha3.VirtualService, error) {
	var httpRoutes []*istionetworkingv1alpha3.HTTPRoute

	// 获取重写配置，在函数开头定义，以便在整个函数中使用
	rewriteConfig := routeRequest.Rewrite

	// 为每个模型创建一个路由规则
	for _, service := range services {
		// 创建匹配请求规则
		httpMatches := []*istionetworkingv1alpha3.HTTPMatchRequest{
			{
				Gateways: []string{
					gatewayNamespace + "/gateway-internal",
				},
				Uri: createUriMatchRequest(
					routeRequest.MatchRules.PathRule.MatchType,
					routeRequest.MatchRules.PathRule.Value,
					routeRequest.MatchRules.PathRule.CaseSensitive,
				),
				Headers: map[string]*istionetworkingv1alpha3.StringMatch{
					"x-model-header": {
						MatchType: &istionetworkingv1alpha3.StringMatch_Prefix{
							Prefix: service.ModelName,
						},
					},
				},
			},
		}

		// 添加HTTP方法匹配
		if len(routeRequest.MatchRules.Methods) > 0 {
			httpMatches[0].Method = createMethodMatchRequest(routeRequest.MatchRules.Methods)
		}

		// 添加其他Headers匹配
		if len(routeRequest.MatchRules.Headers) > 0 {
			additionalHeaders := createHeaderMatchRequests(routeRequest.MatchRules.Headers)
			for key, value := range additionalHeaders {
				httpMatches[0].Headers[key] = value
			}
		}

		// 添加QueryParams匹配
		if len(routeRequest.MatchRules.QueryParams) > 0 {
			httpMatches[0].QueryParams = createQueryParamMatchRequests(routeRequest.MatchRules.QueryParams)
		}

		// 创建路由
		httpRoute := &istionetworkingv1alpha3.HTTPRoute{
			Name:  routeRequest.RouteName,
			Match: httpMatches,
			Route: []*istionetworkingv1alpha3.HTTPRouteDestination{
				{
					Destination: &istionetworkingv1alpha3.Destination{
						Host: service.ServiceName + "." + service.Namespace + ".svc.cluster.local",
						Port: &istionetworkingv1alpha3.PortSelector{
							Number: uint32(service.ServicePort),
						},
						Subset: routeRequest.RouteName + "-" + service.ServiceName,
					},
				},
			},
		}

		// 根据请求参数决定是否设置rewrite
		if rewriteConfig.Enabled {
			ctx.CsmLogger().Infof("多服务按模型分发：为模型%s启用路径重写，重写到: %s",
				service.ModelName, rewriteConfig.Path)
			httpRoute.Rewrite = &istionetworkingv1alpha3.HTTPRewrite{
				Uri: rewriteConfig.Path,
			}
		} else {
			ctx.CsmLogger().Infof("多服务按模型分发：为模型%s未启用路径重写，保持原始路径", service.ModelName)
			// 当rewrite为false时，不设置Rewrite字段，保持原始路径
		}

		// 1. 超时策略
		if routeRequest.TimeoutPolicy != nil && routeRequest.TimeoutPolicy.Enabled {
			ctx.CsmLogger().Infof("设置超时策略: %ds", routeRequest.TimeoutPolicy.Timeout)
			httpRoute.Timeout = durationpb.New(time.Duration(routeRequest.TimeoutPolicy.Timeout) * time.Second)
		}
		// 2. 重试策略
		if routeRequest.RetryPolicy != nil && routeRequest.RetryPolicy.Enabled {
			ctx.CsmLogger().Infof("设置重试策略: attempts=%d, retryOn=%s", routeRequest.RetryPolicy.NumRetries, routeRequest.RetryPolicy.RetryConditions)
			httpRoute.Retries = &istionetworkingv1alpha3.HTTPRetry{
				Attempts: int32(routeRequest.RetryPolicy.NumRetries),
				RetryOn:  routeRequest.RetryPolicy.RetryConditions,
			}
		}

		httpRoutes = append(httpRoutes, httpRoute)
	}

	// 创建VirtualService对象
	virtualService := &v1alpha3.VirtualService{
		TypeMeta: metav1.TypeMeta{
			Kind:       "VirtualService",
			APIVersion: "networking.istio.io/v1alpha3",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      routeRequest.RouteName,
			Namespace: gatewayNamespace,
			Annotations: map[string]string{
				"update-time":                   time.Now().Format("2006-01-02 15:04:05"),
				"path-case-sensitive":           strconv.FormatBool(routeRequest.MatchRules.PathRule.CaseSensitive),
				"auth.higress.io/enabled":       strconv.FormatBool(routeRequest.AuthEnabled),
				"multi-service":                 "true",
				"traffic-distribution-strategy": "model_name",
				"rewrite-enabled":               strconv.FormatBool(rewriteConfig.Enabled), // 新增注解记录rewrite状态
				"src-product":                   routeRequest.SrcProduct,                   // 新增srcProduct注解
			},
		},
		Spec: istionetworkingv1alpha3.VirtualService{
			Hosts: []string{"*"},
			Gateways: []string{
				gatewayNamespace + "/gateway-internal",
			},
			ExportTo: []string{"."},
			Http:     httpRoutes,
		},
	}

	return virtualService, nil
}

// createDestinationRulesIfNeeded 创建DestinationRule（总是创建以支持subset）
func (core *APIServerCore) createDestinationRulesIfNeeded(
	ctx csmContext.CsmContext,
	routeRequest *meta.AIRouteRequest,
	gatewayNamespace string,
	istioClient istioclientset.Interface,
) error {
	if routeRequest.MultiService {
		// 多服务模式
		services, err := routeRequest.GetMultiTargetServices()
		if err != nil {
			return err
		}

		for _, service := range services {
			if err := core.createDestinationRule(ctx, routeRequest.RouteName, service, gatewayNamespace, istioClient); err != nil {
				ctx.CsmLogger().Errorf("创建DestinationRule失败 for service %s: %v", service.ServiceName, err)
				// 继续处理其他服务
			}
		}
	} else {
		// 单服务模式
		service, err := routeRequest.GetSingleTargetService()
		if err != nil {
			return err
		}

		if err := core.createDestinationRule(ctx, routeRequest.RouteName, *service, gatewayNamespace, istioClient); err != nil {
			ctx.CsmLogger().Errorf("创建DestinationRule失败: %v", err)
			return err
		}
	}

	return nil
}

// createDestinationRule 创建单个DestinationRule
func (core *APIServerCore) createDestinationRule(
	ctx csmContext.CsmContext,
	routeName string,
	service meta.TargetService,
	gatewayNamespace string,
	istioClient istioclientset.Interface,
) error {
	destinationRule := &v1alpha3.DestinationRule{
		TypeMeta: metav1.TypeMeta{
			Kind:       "DestinationRule",
			APIVersion: "networking.istio.io/v1beta1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      routeName + "-" + service.ServiceName + "-dr",
			Namespace: gatewayNamespace,
		},
	}

	// 构建TrafficPolicy
	trafficPolicy := &istionetworkingv1alpha3.TrafficPolicy{}
	if service.LoadBalanceAlgorithm == "consistent-hash" {
		ctx.CsmLogger().Infof("设置哈希一致性负载均衡: hashType=%s, hashKey=%s", service.HashType, service.HashKey)
		switch service.HashType {
		case "header":
			trafficPolicy.LoadBalancer = &istionetworkingv1alpha3.LoadBalancerSettings{
				LbPolicy: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHash{
					ConsistentHash: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB{
						HashKey: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_HttpHeaderName{
							HttpHeaderName: service.HashKey,
						},
					},
				},
			}
		case "query_param":
			trafficPolicy.LoadBalancer = &istionetworkingv1alpha3.LoadBalancerSettings{
				LbPolicy: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHash{
					ConsistentHash: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB{
						HashKey: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_HttpQueryParameterName{
							HttpQueryParameterName: service.HashKey,
						},
					},
				},
			}
		case "ip":
			trafficPolicy.LoadBalancer = &istionetworkingv1alpha3.LoadBalancerSettings{
				LbPolicy: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHash{
					ConsistentHash: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB{
						HashKey: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_UseSourceIp{
							UseSourceIp: true,
						},
					},
				},
			}
		case "cookie":
			trafficPolicy.LoadBalancer = &istionetworkingv1alpha3.LoadBalancerSettings{
				LbPolicy: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHash{
					ConsistentHash: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB{
						HashKey: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_HttpCookie{
							HttpCookie: &istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_HTTPCookie{
								Name: service.HashKey,
							},
						},
					},
				},
			}
		}
	} else if service.LoadBalanceAlgorithm != "" {
		trafficPolicy.LoadBalancer = &istionetworkingv1alpha3.LoadBalancerSettings{
			LbPolicy: &istionetworkingv1alpha3.LoadBalancerSettings_Simple{
				Simple: istionetworkingv1alpha3.LoadBalancerSettings_SimpleLB(
					istionetworkingv1alpha3.LoadBalancerSettings_SimpleLB_value[getLoadBalancerSimpleType(service.LoadBalanceAlgorithm)],
				),
			},
		}
	}

	// 构建subset配置
	subsetName := routeName + "-" + service.ServiceName
	subset := &istionetworkingv1alpha3.Subset{
		Name: subsetName,
	}

	// 如果有负载均衡配置，将TrafficPolicy应用到subset上
	if service.LoadBalanceAlgorithm != "" {
		subset.TrafficPolicy = trafficPolicy
	}

	destinationRule.Spec = istionetworkingv1alpha3.DestinationRule{
		Host:    service.ServiceName + "." + service.Namespace + ".svc.cluster.local",
		Subsets: []*istionetworkingv1alpha3.Subset{subset},
	}

	// 创建DestinationRule资源
	_, err := istioClient.NetworkingV1alpha3().DestinationRules(gatewayNamespace).Create(
		context.TODO(), destinationRule, metav1.CreateOptions{},
	)
	if err != nil {
		return err
	}

	ctx.CsmLogger().Infof("DestinationRule创建成功: %s", destinationRule.Name)
	return nil
}

// createModelRouteEnvoyFilter 创建模型路由EnvoyFilter
func (core *APIServerCore) createModelRouteEnvoyFilter(
	ctx csmContext.CsmContext,
	routeRequest *meta.AIRouteRequest,
	gatewayNamespace string,
	client kube.Client,
) error {
	ctx.CsmLogger().Infof("开始创建模型路由EnvoyFilter")

	// 获取动态客户端
	dynamicClient := client.Dynamic()
	groupVersionResource := schema.GroupVersionResource{
		Group:    "networking.istio.io",
		Version:  "v1alpha3",
		Resource: "envoyfilters",
	}

	// 检查是否已存在ai-model-route EnvoyFilter
	envoyFilterName := "ai-model-route"
	existingFilter, err := dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).Get(
		context.TODO(), envoyFilterName, metav1.GetOptions{},
	)
	if err == nil {
		// EnvoyFilter已存在，直接跳过
		ctx.CsmLogger().Infof("模型路由EnvoyFilter已存在，跳过创建: %s", existingFilter.GetName())
		return nil
	}

	// 如果不是NotFound错误，返回错误
	if !k8serrors.IsNotFound(err) {
		return fmt.Errorf("检查EnvoyFilter是否存在时发生错误: %v", err)
	}

	// EnvoyFilter不存在，继续创建
	ctx.CsmLogger().Infof("EnvoyFilter不存在，开始创建: %s", envoyFilterName)

	// 读取模板文件
	templatePath := "templates/higress/wasm/model-route.tmpl"
	templateData := map[string]interface{}{
		"Namespace": gatewayNamespace,
	}

	// 使用模板引擎渲染EnvoyFilter
	tmpl, err := template.ParseFiles(templatePath)
	if err != nil {
		return fmt.Errorf("failed to parse template: %v", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, templateData); err != nil {
		return fmt.Errorf("failed to execute template: %v", err)
	}

	// 解析YAML为Kubernetes对象
	decoder := yaml.NewYAMLOrJSONDecoder(bytes.NewReader(buf.Bytes()), 4096)
	var envoyFilter unstructured.Unstructured
	if err := decoder.Decode(&envoyFilter); err != nil {
		return fmt.Errorf("failed to decode EnvoyFilter YAML: %v", err)
	}

	// 设置EnvoyFilter名称
	envoyFilter.SetName(envoyFilterName)
	envoyFilter.SetNamespace(gatewayNamespace)

	// 创建EnvoyFilter资源
	_, err = dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).Create(
		context.TODO(), &envoyFilter, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("failed to create EnvoyFilter: %v", err)
	}

	ctx.CsmLogger().Infof("模型路由EnvoyFilter创建成功: %s", envoyFilter.GetName())
	return nil
}

// deleteOldDestinationRules 删除旧的DestinationRules
func (core *APIServerCore) deleteOldDestinationRules(
	ctx csmContext.CsmContext,
	routeName, gatewayNamespace string,
	istioClient istioclientset.Interface,
) error {
	// 删除单服务模式的DestinationRule
	singleServiceDRName := routeName + "-dr"
	err := istioClient.NetworkingV1alpha3().DestinationRules(gatewayNamespace).Delete(
		context.TODO(), singleServiceDRName, metav1.DeleteOptions{},
	)
	if err != nil && !k8serrors.IsNotFound(err) {
		ctx.CsmLogger().Errorf("删除单服务DestinationRule失败: %v", err)
	}

	// 删除多服务模式的DestinationRules（通过标签选择器）
	drList, err := istioClient.NetworkingV1alpha3().DestinationRules(gatewayNamespace).List(
		context.TODO(), metav1.ListOptions{},
	)
	if err != nil {
		return err
	}

	for _, dr := range drList.Items {
		// 检查是否是该路由相关的DestinationRule
		if strings.HasPrefix(dr.Name, routeName+"-") && strings.HasSuffix(dr.Name, "-dr") {
			err := istioClient.NetworkingV1alpha3().DestinationRules(gatewayNamespace).Delete(
				context.TODO(), dr.Name, metav1.DeleteOptions{},
			)
			if err != nil && !k8serrors.IsNotFound(err) {
				ctx.CsmLogger().Errorf("删除DestinationRule %s 失败: %v", dr.Name, err)
			} else {
				ctx.CsmLogger().Infof("删除DestinationRule成功: %s", dr.Name)
			}
		}
	}

	return nil
}

// deleteModelRouteEnvoyFilter 删除模型路由EnvoyFilter
func (core *APIServerCore) deleteModelRouteEnvoyFilter(ctx csmContext.CsmContext, routeName, gatewayNamespace string, client kube.Client) error {
	dynamicClient := client.Dynamic()
	groupVersionResource := schema.GroupVersionResource{
		Group:    "networking.istio.io",
		Version:  "v1alpha3",
		Resource: "envoyfilters",
	}

	envoyFilterName := "ai-model-route-" + routeName
	err := dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).Delete(
		context.TODO(), envoyFilterName, metav1.DeleteOptions{})
	if err != nil && !k8serrors.IsNotFound(err) {
		return fmt.Errorf("failed to delete EnvoyFilter: %v", err)
	}

	if err == nil {
		ctx.CsmLogger().Infof("模型路由EnvoyFilter删除成功: %s", envoyFilterName)
	}
	return nil
}

// extractMatchRulesFromHttpRoute 从HTTP路由中提取匹配规则
func (core *APIServerCore) extractMatchRulesFromHttpRoute(vs *v1alpha3.VirtualService, httpRoute *istionetworkingv1alpha3.HTTPRoute) map[string]interface{} {
	// 初始化匹配规则（不包含rewrite）
	matchRules := map[string]interface{}{
		"pathRule": map[string]interface{}{
			"matchType":     "prefix",
			"value":         "/",
			"caseSensitive": true,
		},
		"methods":     []string{},
		"headers":     []map[string]interface{}{},
		"queryParams": []map[string]interface{}{},
	}

	// 提取匹配规则
	if len(httpRoute.Match) > 0 && httpRoute.Match[0].Uri != nil {
		uri := httpRoute.Match[0].Uri
		pathRule := matchRules["pathRule"].(map[string]interface{})

		// 确定匹配类型和值
		if uri.GetExact() != "" {
			pathRule["matchType"] = "exact"
			pathRule["value"] = uri.GetExact()
		} else if uri.GetPrefix() != "" {
			pathRule["matchType"] = "prefix"
			pathRule["value"] = uri.GetPrefix()
		} else if uri.GetRegex() != "" {
			pathRule["matchType"] = "regex"
			pathRule["value"] = uri.GetRegex()
		}

		// 读取路径大小写敏感设置
		if vs.Annotations != nil {
			if caseSensitive, exists := vs.Annotations["path-case-sensitive"]; exists {
				if caseSensitive == "false" {
					pathRule["caseSensitive"] = false
				}
			}
		}
	}

	if len(httpRoute.Match) > 0 {
		// 提取HTTP方法
		if httpRoute.Match[0].Method != nil {
			// 先检查是否为精确匹配
			methodExact := httpRoute.Match[0].Method.GetExact()
			if methodExact != "" {
				matchRules["methods"] = []string{methodExact}
			} else {
				// 检查是否为正则表达式匹配
				methodRegex := httpRoute.Match[0].Method.GetRegex()
				if methodRegex != "" {
					// 尝试解析正则表达式 ^(GET|POST|PUT)$ 格式的方法匹配
					regex := methodRegex
					if strings.HasPrefix(regex, "^(") && strings.HasSuffix(regex, ")$") {
						// 提取中间的方法列表
						methods := strings.Split(regex[2:len(regex)-2], "|")
						matchRules["methods"] = methods
					}
				}
			}
		}

		// 提取请求头匹配规则
		if len(httpRoute.Match[0].Headers) > 0 {
			headers := []map[string]interface{}{}
			for key, stringMatch := range httpRoute.Match[0].Headers {
				// 跳过内部使用的 x-model-header
				if key == "x-model-header" {
					continue
				}

				header := map[string]interface{}{
					"key": key,
				}

				if stringMatch.GetExact() != "" {
					header["matchType"] = "exact"
					header["value"] = stringMatch.GetExact()
				} else if stringMatch.GetPrefix() != "" {
					header["matchType"] = "prefix"
					header["value"] = stringMatch.GetPrefix()
				}

				headers = append(headers, header)
			}
			matchRules["headers"] = headers
		}

		// 提取查询参数匹配规则
		if len(httpRoute.Match[0].QueryParams) > 0 {
			queryParams := []map[string]interface{}{}
			for key, stringMatch := range httpRoute.Match[0].QueryParams {
				queryParam := map[string]interface{}{
					"key": key,
				}

				if stringMatch.GetExact() != "" {
					queryParam["matchType"] = "exact"
					queryParam["value"] = stringMatch.GetExact()
				} else if stringMatch.GetPrefix() != "" {
					queryParam["matchType"] = "prefix"
					queryParam["value"] = stringMatch.GetPrefix()
				}

				queryParams = append(queryParams, queryParam)
			}
			matchRules["queryParams"] = queryParams
		}
	}

	return matchRules
}

// extractSingleTargetService 提取单服务目标服务信息
func (core *APIServerCore) extractSingleTargetService(
	vs *v1alpha3.VirtualService,
	httpRoute *istionetworkingv1alpha3.HTTPRoute,
	routeName string,
	istioClient istioclientset.Interface,
	gatewayNamespace string,
) map[string]interface{} {
	targetService := map[string]interface{}{
		"serviceSource":        "CCE",
		"serviceName":          "",
		"namespace":            "",
		"servicePort":          0,
		"loadBalanceAlgorithm": "round-robin", // 默认值
		"hashType":             "",
		"hashKey":              "",
	}

	if len(httpRoute.Route) > 0 && httpRoute.Route[0].Destination != nil {
		dest := httpRoute.Route[0].Destination
		if dest.Host != "" {
			parts := strings.Split(dest.Host, ".")
			if len(parts) >= 2 {
				targetService["serviceName"] = parts[0]
				targetService["namespace"] = parts[1]
			}
		}
		if dest.Port != nil {
			targetService["servicePort"] = dest.Port.Number
		}
	}

	// 从annotations中获取负载均衡算法
	if vs.Annotations != nil {
		if algo, exists := vs.Annotations["load-balance-algorithm"]; exists && algo != "" {
			targetService["loadBalanceAlgorithm"] = algo
		}
	}

	// 获取详细的负载均衡算法配置，包括哈希一致性配置
	core.extractLoadBalanceConfigWithHashInfo(targetService, routeName, istioClient, gatewayNamespace)

	return targetService
}

// extractMultiTargetServices 提取多服务目标服务信息
func (core *APIServerCore) extractMultiTargetServices(
	vs *v1alpha3.VirtualService,
	httpRoute *istionetworkingv1alpha3.HTTPRoute,
	routeName string,
	istioClient istioclientset.Interface,
	gatewayNamespace string,
	trafficDistributionStrategy string) []map[string]interface{} {

	switch trafficDistributionStrategy {
	case "ratio":
		return core.extractRatioBasedServices(httpRoute, routeName, istioClient, gatewayNamespace)
	case "model_name":
		return core.extractModelNameBasedServices(vs, httpRoute, routeName, istioClient, gatewayNamespace)
	default:
		return core.extractDefaultServices(httpRoute)
	}
}

// extractRatioBasedServices 提取按比例分发的服务信息
func (core *APIServerCore) extractRatioBasedServices(
	httpRoute *istionetworkingv1alpha3.HTTPRoute,
	routeName string,
	istioClient istioclientset.Interface,
	gatewayNamespace string) []map[string]interface{} {

	var targetServices []map[string]interface{}
	for _, route := range httpRoute.Route {
		if route.Destination == nil {
			continue
		}

		targetService := core.createBaseTargetService()
		core.parseDestinationInfo(targetService, route.Destination)

		// 添加权重信息
		if route.Weight > 0 {
			targetService["requestRatio"] = int(route.Weight)
		}

		// 获取负载均衡算法
		core.setLoadBalanceAlgorithm(targetService, routeName, istioClient, gatewayNamespace)
		targetServices = append(targetServices, targetService)
	}
	return targetServices
}

// extractModelNameBasedServices 提取按模型名称分发的服务信息
func (core *APIServerCore) extractModelNameBasedServices(
	vs *v1alpha3.VirtualService,
	httpRoute *istionetworkingv1alpha3.HTTPRoute,
	routeName string,
	istioClient istioclientset.Interface,
	gatewayNamespace string) []map[string]interface{} {

	if len(httpRoute.Route) == 0 || httpRoute.Route[0].Destination == nil {
		return []map[string]interface{}{}
	}

	targetService := core.createBaseTargetService()
	core.parseDestinationInfo(targetService, httpRoute.Route[0].Destination)
	core.extractModelName(targetService, vs, httpRoute)
	core.setLoadBalanceAlgorithm(targetService, routeName, istioClient, gatewayNamespace)

	return []map[string]interface{}{targetService}
}

// extractDefaultServices 提取默认服务信息
func (core *APIServerCore) extractDefaultServices(
	httpRoute *istionetworkingv1alpha3.HTTPRoute) []map[string]interface{} {

	var targetServices []map[string]interface{}
	for _, route := range httpRoute.Route {
		if route.Destination == nil {
			continue
		}

		targetService := core.createBaseTargetService()
		core.parseDestinationInfo(targetService, route.Destination)
		targetServices = append(targetServices, targetService)
	}
	return targetServices
}

// createBaseTargetService 创建基础目标服务结构
func (core *APIServerCore) createBaseTargetService() map[string]interface{} {
	return map[string]interface{}{
		"serviceSource":        "CCE",
		"serviceName":          "",
		"namespace":            "",
		"servicePort":          0,
		"loadBalanceAlgorithm": "round-robin", // 默认值
		"hashType":             "",
		"hashKey":              "",
	}
}

// parseDestinationInfo 解析目标服务信息
func (core *APIServerCore) parseDestinationInfo(
	targetService map[string]interface{},
	dest *istionetworkingv1alpha3.Destination) {

	if dest.Host != "" {
		parts := strings.Split(dest.Host, ".")
		if len(parts) >= 2 {
			targetService["serviceName"] = parts[0]
			targetService["namespace"] = parts[1]
		}
	}
	if dest.Port != nil {
		targetService["servicePort"] = dest.Port.Number
	}
}

// extractModelName 提取模型名称
func (core *APIServerCore) extractModelName(
	targetService map[string]interface{},
	vs *v1alpha3.VirtualService,
	httpRoute *istionetworkingv1alpha3.HTTPRoute) {

	// 从HTTP路由的匹配条件中提取模型名称
	if len(httpRoute.Match) > 0 && httpRoute.Match[0].Headers != nil {
		if modelHeader, exists := httpRoute.Match[0].Headers["x-model-header"]; exists {
			if modelHeader.GetPrefix() != "" {
				targetService["modelName"] = strings.TrimPrefix(modelHeader.GetPrefix(), "model/")
				return
			} else if modelHeader.GetExact() != "" {
				targetService["modelName"] = strings.TrimPrefix(modelHeader.GetExact(), "model/")
				return
			}
		}
	}

	// 如果没有从header中获取到模型名称，尝试从annotations中获取
	serviceName := targetService["serviceName"].(string)
	if serviceName != "" {
		if modelName, exists := vs.Annotations["model-name-"+serviceName]; exists {
			targetService["modelName"] = modelName
		} else {
			// 如果都没有，使用服务名称作为模型名称
			targetService["modelName"] = serviceName
		}
	}
}

// setLoadBalanceAlgorithm 设置负载均衡算法
func (core *APIServerCore) setLoadBalanceAlgorithm(
	targetService map[string]interface{},
	routeName string,
	istioClient istioclientset.Interface,
	gatewayNamespace string) {

	serviceNameStr := targetService["serviceName"].(string)
	destinationRuleName := fmt.Sprintf("%s-%s-dr", routeName, serviceNameStr)
	dr, err := istioClient.NetworkingV1alpha3().DestinationRules(gatewayNamespace).Get(
		context.TODO(), destinationRuleName, metav1.GetOptions{},
	)
	if err != nil || dr == nil {
		return
	}

	// 查找对应的subset，负载均衡配置在subset级别
	subsetName := routeName + "-" + serviceNameStr
	var trafficPolicy *istionetworkingv1alpha3.TrafficPolicy

	// 从subsets中查找对应的subset
	for _, subset := range dr.Spec.Subsets {
		if subset.Name == subsetName && subset.TrafficPolicy != nil {
			trafficPolicy = subset.TrafficPolicy
			break
		}
	}

	if trafficPolicy == nil || trafficPolicy.LoadBalancer == nil {
		return
	}

	// 检查是否为哈希一致性负载均衡
	if consistentHash, ok := trafficPolicy.LoadBalancer.LbPolicy.(*istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHash); ok && consistentHash != nil {
		targetService["loadBalanceAlgorithm"] = "consistent-hash"

		// 提取哈希配置
		if consistentHash.ConsistentHash != nil {
			hashConfig := consistentHash.ConsistentHash

			if httpHeaderName, ok := hashConfig.HashKey.(*istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_HttpHeaderName); ok {
				targetService["hashType"] = "header"
				targetService["hashKey"] = httpHeaderName.HttpHeaderName
			} else if queryParam, ok := hashConfig.HashKey.(*istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_HttpQueryParameterName); ok {
				targetService["hashType"] = "query_param"
				targetService["hashKey"] = queryParam.HttpQueryParameterName
			} else if httpCookie, ok := hashConfig.HashKey.(*istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_HttpCookie); ok {
				targetService["hashType"] = "cookie"
				targetService["hashKey"] = httpCookie.HttpCookie.Name
			} else if _, ok := hashConfig.HashKey.(*istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_UseSourceIp); ok {
				targetService["hashType"] = "ip"
				targetService["hashKey"] = true
			}
		}
		return
	}

	// 处理普通负载均衡算法
	simple, ok := trafficPolicy.LoadBalancer.LbPolicy.(*istionetworkingv1alpha3.LoadBalancerSettings_Simple)
	if !ok || simple == nil {
		return
	}

	switch simple.Simple {
	case istionetworkingv1alpha3.LoadBalancerSettings_ROUND_ROBIN:
		targetService["loadBalanceAlgorithm"] = "round-robin"
	case istionetworkingv1alpha3.LoadBalancerSettings_LEAST_CONN:
		targetService["loadBalanceAlgorithm"] = "least-conn"
	case istionetworkingv1alpha3.LoadBalancerSettings_RANDOM:
		targetService["loadBalanceAlgorithm"] = "random"
	}
}

// getTokenRateLimitInfo 获取Token限流信息
func (core *APIServerCore) getTokenRateLimitInfo(
	ctx csmContext.CsmContext,
	routeName, gatewayNamespace string,
	client kube.Client,
) *meta.TokenRateLimitResponse {
	ctx.CsmLogger().Infof("获取Token限流信息: routeName=%s", routeName)

	// 查找ai-token-limit插件
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := client.Dynamic()
	plugin, err := dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).Get(
		context.TODO(), constants.GetAITokenLimitPluginName(), metav1.GetOptions{},
	)

	if err == nil && plugin != nil {
		ctx.CsmLogger().Infof("找到ai-token-limit插件，提取路由%s的限流信息", routeName)

		// 解析Token限流配置，传入路由名称以过滤特定路由的规则
		tokenRateLimitRuleItems, isEnabled, ruleName := extractTokenRateLimitInfoForRoute(plugin, routeName)

		if isEnabled && len(tokenRateLimitRuleItems) > 0 {
			// 创建响应对象
			return &meta.TokenRateLimitResponse{
				RuleName:  ruleName,
				Enabled:   true,
				RuleItems: tokenRateLimitRuleItems,
			}
		} else if isEnabled {
			// 插件存在且启用，但没有规则项
			return &meta.TokenRateLimitResponse{
				RuleName: ruleName,
				Enabled:  true,
			}
		} else {
			// 插件存在但未启用
			return &meta.TokenRateLimitResponse{
				RuleName: ruleName,
				Enabled:  false,
			}
		}
	} else if k8serrors.IsNotFound(err) {
		ctx.CsmLogger().Infof("未找到ai-token-limit插件")
		// 未找到插件，设置为未启用
		return &meta.TokenRateLimitResponse{
			Enabled: false,
		}
	} else {
		ctx.CsmLogger().Errorf("获取ai-token-limit插件失败: %v", err)
		// 发生错误，设置为未启用
		return &meta.TokenRateLimitResponse{
			Enabled: false,
		}
	}
}

// 创建URI匹配规则
func createUriMatchRequest(matchType, value string, caseSensitive bool) *istionetworkingv1alpha3.StringMatch {
	// 如果不区分大小写，则将value转为小写
	if !caseSensitive {
		value = strings.ToLower(value)
	}

	switch strings.ToLower(matchType) {
	case "prefix":
		return &istionetworkingv1alpha3.StringMatch{
			MatchType: &istionetworkingv1alpha3.StringMatch_Prefix{
				Prefix: value,
			},
		}
	case "exact":
		return &istionetworkingv1alpha3.StringMatch{
			MatchType: &istionetworkingv1alpha3.StringMatch_Exact{
				Exact: value,
			},
		}
	case "regex":
		return &istionetworkingv1alpha3.StringMatch{
			MatchType: &istionetworkingv1alpha3.StringMatch_Regex{
				Regex: value,
			},
		}
	default:
		// 默认使用前缀匹配
		return &istionetworkingv1alpha3.StringMatch{
			MatchType: &istionetworkingv1alpha3.StringMatch_Prefix{
				Prefix: value,
			},
		}
	}
}

// 创建HTTP方法匹配规则
func createMethodMatchRequest(methods []string) *istionetworkingv1alpha3.StringMatch {
	if len(methods) == 0 {
		return nil
	}

	if len(methods) == 1 {
		// 只有一个方法时使用精确匹配
		return &istionetworkingv1alpha3.StringMatch{
			MatchType: &istionetworkingv1alpha3.StringMatch_Exact{
				Exact: methods[0],
			},
		}
	}

	// 多个方法时使用正则表达式匹配
	// 格式如: ^(GET|POST|PUT)$
	regex := "^(" + strings.Join(methods, "|") + ")$"
	return &istionetworkingv1alpha3.StringMatch{
		MatchType: &istionetworkingv1alpha3.StringMatch_Regex{
			Regex: regex,
		},
	}
}

// 创建Header匹配规则
func createHeaderMatchRequests(headers []struct {
	Key       string `json:"key" valid:"required"`
	MatchType string `json:"matchType" valid:"required"`
	Value     string `json:"value" valid:"required"`
}) map[string]*istionetworkingv1alpha3.StringMatch {
	result := make(map[string]*istionetworkingv1alpha3.StringMatch)

	for _, header := range headers {
		var stringMatch *istionetworkingv1alpha3.StringMatch

		switch strings.ToLower(header.MatchType) {
		case "prefix":
			stringMatch = &istionetworkingv1alpha3.StringMatch{
				MatchType: &istionetworkingv1alpha3.StringMatch_Prefix{
					Prefix: header.Value,
				},
			}
		case "exact":
			stringMatch = &istionetworkingv1alpha3.StringMatch{
				MatchType: &istionetworkingv1alpha3.StringMatch_Exact{
					Exact: header.Value,
				},
			}
		default:
			// 默认使用精确匹配
			stringMatch = &istionetworkingv1alpha3.StringMatch{
				MatchType: &istionetworkingv1alpha3.StringMatch_Exact{
					Exact: header.Value,
				},
			}
		}

		result[header.Key] = stringMatch
	}

	return result
}

// 创建QueryParam匹配规则
func createQueryParamMatchRequests(queryParams []struct {
	Key       string `json:"key" valid:"required"`
	MatchType string `json:"matchType" valid:"required"`
	Value     string `json:"value" valid:"required"`
}) map[string]*istionetworkingv1alpha3.StringMatch {
	result := make(map[string]*istionetworkingv1alpha3.StringMatch)

	for _, param := range queryParams {
		var stringMatch *istionetworkingv1alpha3.StringMatch

		switch strings.ToLower(param.MatchType) {
		case "prefix":
			stringMatch = &istionetworkingv1alpha3.StringMatch{
				MatchType: &istionetworkingv1alpha3.StringMatch_Prefix{
					Prefix: param.Value,
				},
			}
		case "exact":
			stringMatch = &istionetworkingv1alpha3.StringMatch{
				MatchType: &istionetworkingv1alpha3.StringMatch_Exact{
					Exact: param.Value,
				},
			}
		default:
			// 默认使用精确匹配
			stringMatch = &istionetworkingv1alpha3.StringMatch{
				MatchType: &istionetworkingv1alpha3.StringMatch_Exact{
					Exact: param.Value,
				},
			}
		}

		result[param.Key] = stringMatch
	}

	return result
}

// UpdateAIGateway 更新AI网关实例的基本信息
func (core *APIServerCore) UpdateAIGateway(ctx csmContext.CsmContext) error {
	// 获取实例ID
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}

	ctx.CsmLogger().Infof("开始更新AI网关实例，实例ID: %s", instanceId)

	// 绑定请求参数
	updateRequest := &meta.UpdateAIGatewayRequest{}

	if err := ctx.Bind(updateRequest); err != nil {
		ctx.CsmLogger().Errorf("绑定请求参数失败，实例ID: %s，错误: %v", instanceId, err)
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 调用服务层更新网关实例
	result, err := core.aiIngressService.UpdateAIGateway(ctx, instanceId, updateRequest)
	if err != nil {
		ctx.CsmLogger().Errorf("更新AI网关实例失败，实例ID: %s，错误: %v", instanceId, err)
		return err
	}

	ctx.CsmLogger().Infof("成功更新AI网关实例，实例ID: %s", instanceId)

	return ctx.JSON(http.StatusOK, result)
}

// RemoveClusterFromAIGateway 从AI网关实例中移除关联的集群
func (core *APIServerCore) RemoveClusterFromAIGateway(ctx csmContext.CsmContext) error {
	// 获取实例ID和集群ID
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}

	clusterId := ctx.Param("ClusterId")
	if clusterId == "" {
		return csmErr.NewMissingParametersException("ClusterId")
	}

	// 调用服务层移除关联集群
	err := core.aiIngressService.RemoveClusterFromAIGateway(ctx, instanceId, clusterId)
	if err != nil {
		return err
	}

	// 构建响应
	response := struct {
		Success bool        `json:"success"`
		Status  int         `json:"status"`
		Result  interface{} `json:"result"`
	}{
		Success: true,
		Status:  http.StatusOK,
		Result:  nil,
	}

	return ctx.JSON(http.StatusOK, response)
}

// GetServicePortInfo 根据集群ID、服务名称和命名空间查询服务端口信息
func (core *APIServerCore) GetServicePortInfo(ctx csmContext.CsmContext) error {
	// 获取路径参数
	clusterId := ctx.Param("clusterId")
	if clusterId == "" {
		return csmErr.NewMissingParametersException("clusterId")
	}

	serviceName := ctx.Param("serviceName")
	if serviceName == "" {
		return csmErr.NewMissingParametersException("serviceName")
	}

	namespace := ctx.Param("namespace")
	if namespace == "" {
		return csmErr.NewMissingParametersException("namespace")
	}

	// 获取区域
	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return csmErr.NewMissingParametersException("X-Region header")
	}

	// 创建k8s客户端
	client, err := core.cceService.NewClient(ctx, region, clusterId, meta.StandaloneMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to create cluster client: %v", err)
		return csmErr.NewDBOperationException(err)
	}

	// 获取服务
	svc, err := client.Kube().CoreV1().Services(namespace).Get(context.TODO(), serviceName, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return csmErr.NewResourceNotFoundException(fmt.Sprintf("Service %s not found in namespace %s", serviceName, namespace), err)
		}
		ctx.CsmLogger().Errorf("Failed to get service %s in namespace %s: %v", serviceName, namespace, err)
		return csmErr.NewDBOperationException(err)
	}

	// 提取端口信息
	portInfoList := make([]string, 0, len(svc.Spec.Ports))
	for _, port := range svc.Spec.Ports {
		portInfo := fmt.Sprintf("%d %s", port.Port, string(port.Protocol))
		portInfoList = append(portInfoList, portInfo)
	}

	// 按照端口号从小到大排序
	sort.Slice(portInfoList, func(i, j int) bool {
		// 格式为 "端口号 协议类型"，需要从字符串中提取端口号
		portI, _ := strconv.Atoi(strings.Split(portInfoList[i], " ")[0])
		portJ, _ := strconv.Atoi(strings.Split(portInfoList[j], " ")[0])
		return portI < portJ
	})

	// 返回成功响应
	response := struct {
		Success bool     `json:"success"`
		Status  int      `json:"status"`
		Result  []string `json:"result"`
	}{
		Success: true,
		Status:  http.StatusOK,
		Result:  portInfoList,
	}

	return ctx.JSON(http.StatusOK, response.Result)
}

// ListRoutes 查询路由列表
func (core *APIServerCore) ListRoutes(ctx csmContext.CsmContext) error {
	// 获取路径参数 - 实例ID
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	// 获取请求头中的区域信息
	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return csmErr.NewMissingParametersException("X-Region header")
	}

	// 获取查询参数
	routeName := ctx.QueryParam("routeName")

	// 获取分页参数
	pageNo := 1
	pageSize := 10
	orderBy := "createTime"
	order := "desc"

	// 从查询参数中获取分页信息
	if pageNoStr := ctx.QueryParam("pageNo"); pageNoStr != "" {
		if val, err := strconv.Atoi(pageNoStr); err == nil && val > 0 {
			pageNo = val
		}
	}
	if pageSizeStr := ctx.QueryParam("pageSize"); pageSizeStr != "" {
		if val, err := strconv.Atoi(pageSizeStr); err == nil && val > 0 {
			pageSize = val
		}
	}
	if orderByStr := ctx.QueryParam("orderBy"); orderByStr != "" {
		orderBy = orderByStr
	}
	if orderStr := ctx.QueryParam("order"); orderStr != "" {
		order = orderStr
	}

	// 获取目标网关的命名空间
	gatewayNamespace := "istio-system-" + instanceId

	// 创建集群客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	client, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		return errors.Wrap(err, "Failed to create Istio client")
	}
	istioClient := client.Istio()

	// 查询VirtualService资源列表
	vsList, err := istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			// 返回空列表，符合文档规范
			return ctx.JSON(http.StatusOK, map[string]interface{}{
				"success": true,
				"status":  http.StatusOK,
				"page": map[string]interface{}{
					"orderBy":    orderBy,
					"order":      order,
					"pageNo":     pageNo,
					"pageSize":   pageSize,
					"totalCount": 0,
					"result":     []interface{}{},
				},
			})
		}
		return errors.Wrap(err, "Failed to list VirtualServices")
	}

	// 构建和过滤路由列表
	routes := core.buildRoutesList(vsList, routeName, istioClient, gatewayNamespace)

	// 应用排序
	sortRoutes(routes, orderBy, order)

	// 应用分页
	pagedRoutes, totalCount := applyPagination(routes, pageNo, pageSize)

	// 构造响应，符合文档规范
	response := map[string]interface{}{
		"orderBy":    orderBy,
		"order":      order,
		"pageNo":     pageNo,
		"pageSize":   pageSize,
		"totalCount": totalCount,
		"result":     pagedRoutes,
	}

	return ctx.JSON(http.StatusOK, response)
}

// 提取路径匹配信息的辅助函数
func extractMatchPath(httpRoute *istionetworkingv1alpha3.HTTPRoute) map[string]interface{} {
	// 设置默认值
	matchPath := map[string]interface{}{
		"matchType": "prefix",
		"value":     "/",
	}

	// 如果没有匹配条件，返回默认值
	if len(httpRoute.Match) == 0 || httpRoute.Match[0].Uri == nil {
		return matchPath
	}

	// 提取匹配类型和值
	uri := httpRoute.Match[0].Uri
	if uri.GetExact() != "" {
		matchPath["matchType"] = "exact"
		matchPath["value"] = uri.GetExact()
	} else if uri.GetPrefix() != "" {
		matchPath["matchType"] = "prefix"
		matchPath["value"] = uri.GetPrefix()
	} else if uri.GetRegex() != "" {
		matchPath["matchType"] = "regex"
		matchPath["value"] = uri.GetRegex()
	}

	return matchPath
}

// 提取目标服务信息的辅助函数
func extractTargetService(httpRoute *istionetworkingv1alpha3.HTTPRoute, vs *v1alpha3.VirtualService) map[string]interface{} {
	targetService := map[string]interface{}{
		"serviceName": "",
		"namespace":   "",
		"servicePort": 0,
	}

	// 如果没有路由目标，返回默认值
	if len(httpRoute.Route) == 0 || httpRoute.Route[0].Destination == nil {
		return targetService
	}

	// 提取服务信息
	dest := httpRoute.Route[0].Destination
	if dest.Host != "" {
		parts := strings.Split(dest.Host, ".")
		if len(parts) >= 2 {
			targetService["serviceName"] = parts[0]
			targetService["namespace"] = parts[1]
		}
	}
	if dest.Port != nil {
		targetService["servicePort"] = dest.Port.Number
	}

	// 从annotations中获取负载均衡算法
	if vs.Annotations != nil {
		if algo, exists := vs.Annotations["load-balance-algorithm"]; exists && algo != "" {
			targetService["loadBalanceAlgorithm"] = algo
		}
	}

	return targetService
}

// 构建路由列表的辅助函数
func (core *APIServerCore) buildRoutesList(
	vsList *v1alpha3.VirtualServiceList,
	routeName string,
	istioClient istioclientset.Interface,
	gatewayNamespace string,
) []map[string]interface{} {
	routes := make([]map[string]interface{}, 0)

	// 遍历所有VirtualService
	for _, vs := range vsList.Items {
		// 如果指定了路由名称进行过滤，且不匹配则跳过
		if routeName != "" && !strings.Contains(vs.Name, routeName) {
			continue
		}
		// 从注解中获取srcProduct
		srcProduct := ""
		if vs.Annotations != nil {
			if product, exists := vs.Annotations["src-product"]; exists {
				srcProduct = product
			}
		}
		// 对于每个VirtualService，提取路由信息
		if len(vs.Spec.Http) > 0 {
			// 判断路由类型
			isModelBased := len(vs.Spec.Http) > 1
			isRatioBased := len(vs.Spec.Http) == 1 && len(vs.Spec.Http[0].Route) > 1

			if isModelBased {
				// 按模型分发 - 合并所有服务到一个路由条目
				// 使用第一个HTTP路由的匹配路径作为基础
				matchPath := extractMatchPath(vs.Spec.Http[0])

				// 提取所有目标服务
				var allTargetServices []map[string]interface{}
				for _, httpRoute := range vs.Spec.Http {
					services := core.extractMultiTargetServices(&vs, httpRoute, vs.Name, istioClient, gatewayNamespace, "model_name")
					allTargetServices = append(allTargetServices, services...)
				}

				route := map[string]interface{}{
					"routeName":     vs.Name,
					"routeStatus":   "PUBLISHED",
					"matchPath":     matchPath,
					"targetService": allTargetServices,
					"srcProduct":    srcProduct,
					"createTime":    vs.CreationTimestamp.Format("2006-01-02 15:04:05"),
				}

				routes = append(routes, route)
			} else {
				// 对于非模型分发的路由，保持原有逻辑
				for _, httpRoute := range vs.Spec.Http {
					// 提取路径匹配规则
					matchPath := extractMatchPath(httpRoute)

					// 判断路由类型并提取目标服务信息
					var targetServices []map[string]interface{}
					if isRatioBased {
						// 按比例分发
						targetServices = core.extractMultiTargetServices(&vs, httpRoute, vs.Name, istioClient, gatewayNamespace, "ratio")
					} else {
						// 单服务
						targetService := core.extractSingleTargetService(&vs, httpRoute, vs.Name, istioClient, gatewayNamespace)
						targetServices = []map[string]interface{}{targetService}
					}

					route := map[string]interface{}{
						"routeName":     vs.Name,
						"routeStatus":   "PUBLISHED",
						"matchPath":     matchPath,
						"targetService": targetServices,
						"srcProduct":    srcProduct,
						"createTime":    vs.CreationTimestamp.Format("2006-01-02 15:04:05"),
					}

					routes = append(routes, route)
				}
			}
		}
	}

	return routes
}

// 排序路由列表的辅助函数
func sortRoutes(routes []map[string]interface{}, orderBy string, order string) {
	if orderBy == "createTime" {
		sort.Slice(routes, func(i, j int) bool {
			timeI, _ := time.Parse("2006-01-02 15:04:05", routes[i]["createTime"].(string))
			timeJ, _ := time.Parse("2006-01-02 15:04:05", routes[j]["createTime"].(string))
			if order == "desc" {
				return timeI.After(timeJ)
			}
			return timeI.Before(timeJ)
		})
	} else if orderBy == "routeName" {
		sort.Slice(routes, func(i, j int) bool {
			if order == "desc" {
				return routes[i]["routeName"].(string) > routes[j]["routeName"].(string)
			}
			return routes[i]["routeName"].(string) < routes[j]["routeName"].(string)
		})
	}
}

// 应用分页的辅助函数
func applyPagination(routes []map[string]interface{}, pageNo int, pageSize int) ([]map[string]interface{}, int) {
	totalCount := len(routes)

	// 计算分页范围
	startIndex := (pageNo - 1) * pageSize
	endIndex := startIndex + pageSize

	// 边界检查
	if startIndex >= totalCount {
		return []map[string]interface{}{}, totalCount
	}

	if endIndex > totalCount {
		endIndex = totalCount
	}

	return routes[startIndex:endIndex], totalCount
}

func (core *APIServerCore) getAllowedConsumers(
	ctx csmContext.CsmContext,
	instanceId, routeName string,
	client kube.Client,
) ([]string, error) {
	// 获取区域信息
	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return nil, csmErr.NewMissingParametersException("X-Region header")
	}
	dynamicClient := client.Dynamic()
	namespace := fmt.Sprintf("istio-system-%s", instanceId)
	gvr := schema.GroupVersionResource{Group: "extensions.higress.io", Version: "v1alpha1", Resource: "wasmplugins"}
	list, err := dynamicClient.Resource(gvr).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		return nil, errors.Wrap(err, "failed to list WasmPlugins")
	}
	// 收集消费者名称去重
	consumersSet := make(map[string]struct{})
	for _, item := range list.Items {
		name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
		if name != constants.GetKeyAuthPluginName() {
			continue
		}
		matchRules, exists, _ := unstructured.NestedSlice(item.Object, "spec", "matchRules")
		if !exists {
			continue
		}
		for _, ruleItem := range matchRules {
			rule, ok := ruleItem.(map[string]interface{})
			if !ok {
				continue
			}
			ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
			if !exists {
				continue
			}
			// 检查此规则是否包含目标路由
			var matched bool
			for _, ingress := range ingressList {
				if r, ok := ingress.(string); ok && r == routeName {
					matched = true
					break
				}
			}
			if !matched {
				continue
			}
			// 提取允许的消费者列表
			configMap, exists, _ := unstructured.NestedMap(rule, "config")
			if !exists {
				continue
			}
			allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
			if !exists {
				continue
			}
			for _, allowItem := range allowList {
				if consumerName, ok := allowItem.(string); ok {
					consumersSet[consumerName] = struct{}{}
				}
			}
		}
	}
	// 转切片并排序
	var consumers []string
	for name := range consumersSet {
		consumers = append(consumers, name)
	}
	sort.Strings(consumers)

	// 过滤掉虚拟消费者
	consumers = core.filterVirtualConsumer(consumers)
	return consumers, nil
}

// GetRouteDetail 查询路由详情
func (core *APIServerCore) GetRouteDetail(ctx csmContext.CsmContext) error {
	// 获取路径参数 - 实例ID和路由名称
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	routeName := ctx.Param("routeName")
	if routeName == "" {
		return csmErr.NewMissingParametersException("routeName")
	}

	// 获取请求头中的区域信息
	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return csmErr.NewMissingParametersException("X-Region header")
	}

	ctx.CsmLogger().Infof("查询路由详情: instanceId=%s, routeName=%s", instanceId, routeName)

	// 获取目标网关的命名空间
	gatewayNamespace := "istio-system-" + instanceId

	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	// 创建集群客户端
	client, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		return errors.Wrap(err, "Failed to create Istio client")
	}
	istioClient := client.Istio()

	// 获取指定的VirtualService资源
	vs, err := istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).Get(
		context.TODO(), routeName, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return csmErr.NewResourceNotFoundException(fmt.Sprintf("路由不存在"), err)
		}
		return errors.Wrap(err, "Failed to get VirtualService")
	}

	// 初始化返回结果
	result := map[string]interface{}{
		"routeName":  vs.Name,
		"createTime": vs.CreationTimestamp.Format("2006-01-02 15:04:05"),
		"updateTime": vs.CreationTimestamp.Format("2006-01-02 15:04:05"), // 默认使用创建时间
	}

	// 从annotations中获取更新时间，如果存在
	if vs.Annotations != nil {
		if updateTime, exists := vs.Annotations["update-time"]; exists && updateTime != "" {
			result["updateTime"] = updateTime
		}
	}

	// 判断是否为多服务模式
	multiService := false
	trafficDistributionStrategy := ""

	// 检查annotations中的多服务标识
	if vs.Annotations != nil {
		if multiServiceFlag, exists := vs.Annotations["multi-service"]; exists && multiServiceFlag == "true" {
			multiService = true
		}
		if strategy, exists := vs.Annotations["traffic-distribution-strategy"]; exists && strategy != "" {
			trafficDistributionStrategy = strategy
		}
	}

	// 如果没有annotations标识，通过路由配置判断
	if !multiService && len(vs.Spec.Http) > 0 {
		// 检查是否有多个HTTP路由（按模型分发）
		if len(vs.Spec.Http) > 1 {
			multiService = true
			if trafficDistributionStrategy == "" {
				// 检查是否有x-model-header匹配条件
				hasModelHeader := false
				for _, httpRoute := range vs.Spec.Http {
					if len(httpRoute.Match) > 0 && httpRoute.Match[0].Headers != nil {
						if _, exists := httpRoute.Match[0].Headers["x-model-header"]; exists {
							hasModelHeader = true
							break
						}
					}
				}
				if hasModelHeader {
					trafficDistributionStrategy = "model_name"
				}
			}
		} else {
			// 检查单个HTTP路由下是否有多个目标（按比例分发）
			httpRoute := vs.Spec.Http[0]
			if len(httpRoute.Route) > 1 {
				multiService = true
				// 尝试从路由配置推断分发策略
				if trafficDistributionStrategy == "" {
					// 检查是否有权重配置（比例分发）
					hasWeight := false
					for _, route := range httpRoute.Route {
						if route.Weight > 0 {
							hasWeight = true
							break
						}
					}
					if hasWeight {
						trafficDistributionStrategy = "ratio"
					}
				}
			}
		}
	}

	result["multiService"] = multiService
	if multiService && trafficDistributionStrategy != "" {
		result["trafficDistributionStrategy"] = trafficDistributionStrategy
	}

	// 处理HTTP路由
	if len(vs.Spec.Http) > 0 {
		httpRoute := vs.Spec.Http[0]
		matchRules := core.extractMatchRulesFromHttpRoute(vs, httpRoute)
		result["matchRules"] = matchRules

		// 提取rewrite信息到顶级字段
		rewriteInfo := map[string]interface{}{
			"enabled": false,
		}

		// 从注解中检查rewrite是否启用
		if vs.Annotations != nil {
			if rewriteEnabled, exists := vs.Annotations["rewrite-enabled"]; exists {
				if rewriteEnabled == "true" {
					rewriteInfo["enabled"] = true

					// 如果启用了rewrite，尝试从HTTPRewrite中提取path
					if httpRoute.Rewrite != nil && httpRoute.Rewrite.Uri != "" {
						rewriteInfo["path"] = httpRoute.Rewrite.Uri
					}
				}
			}
		}

		result["rewrite"] = rewriteInfo
		ctx.CsmLogger().Infof("路由详情提取rewrite配置: enabled=%v, path=%v",
			rewriteInfo["enabled"], rewriteInfo["path"])

		// 提取超时和重试策略
		ctx.CsmLogger().Infof("开始提取超时和重试策略配置")
		timeoutPolicy, retryPolicy := core.extractTimeoutAndRetryPolicies(httpRoute)
		result["timeoutPolicy"] = timeoutPolicy
		result["retryPolicy"] = retryPolicy
		ctx.CsmLogger().Infof("超时策略提取完成: enabled=%v", timeoutPolicy["enabled"])
		ctx.CsmLogger().Infof("重试策略提取完成: enabled=%v", retryPolicy["enabled"])

		if multiService {
			if len(vs.Spec.Http) > 1 {
				// 按模型名称
				var allTargetServices []map[string]interface{}
				for _, httpRoute := range vs.Spec.Http {
					services := core.extractMultiTargetServices(vs, httpRoute, vs.Name, istioClient, gatewayNamespace, "model_name")
					allTargetServices = append(allTargetServices, services...)
				}
				result["targetService"] = allTargetServices
			} else {
				// 按请求比例
				targetServices := core.extractMultiTargetServices(vs, httpRoute, routeName, istioClient, gatewayNamespace, trafficDistributionStrategy)
				result["targetService"] = targetServices
			}

		} else {
			// 单服务模式：返回单个服务对象
			targetService := core.extractSingleTargetService(vs, httpRoute, routeName, istioClient, gatewayNamespace)
			result["targetService"] = targetService
		}
	}

	// 获取认证信息
	authEnabled := false
	var allowedConsumers []string
	srcProduct := ""

	if vs.Annotations != nil {
		if enabled, exists := vs.Annotations["auth.higress.io/enabled"]; exists && enabled == "true" {
			authEnabled = true
		}
		// 获取srcProduct信息
		if product, exists := vs.Annotations["src-product"]; exists {
			srcProduct = product
		}
	}

	// 获取实际拥有该路由访问权限的消费者列表
	consumers, err := core.getAllowedConsumers(ctx, instanceId, routeName, client)
	if err != nil {
		ctx.CsmLogger().Errorf("获取允许的消费者列表失败: %v", err)
	} else {
		allowedConsumers = consumers
	}

	result["authEnabled"] = authEnabled
	if allowedConsumers == nil {
		result["allowedConsumers"] = []string{}
	} else {
		result["allowedConsumers"] = allowedConsumers
	}

	// 添加srcProduct字段到返回结果
	result["srcProduct"] = srcProduct

	// 获取Token限流信息
	tokenRateLimitResponse := core.getTokenRateLimitInfo(ctx, routeName, gatewayNamespace, client)
	result["tokenRateLimit"] = tokenRateLimitResponse

	ctx.CsmLogger().Infof("路由详情查询成功: routeName=%s, multiService=%v", routeName, multiService)
	return ctx.JSON(http.StatusOK, result)
}

// extractTimeoutAndRetryPolicies 提取超时和重试策略
func (core *APIServerCore) extractTimeoutAndRetryPolicies(httpRoute *istionetworkingv1alpha3.HTTPRoute) (map[string]interface{}, map[string]interface{}) {
	// 提取超时策略
	timeoutPolicy := map[string]interface{}{
		"enabled": false,
	}

	if httpRoute.Timeout != nil {
		timeoutPolicy["enabled"] = true
		timeoutPolicy["timeout"] = int(httpRoute.Timeout.Seconds)
	}

	// 提取重试策略
	retryPolicy := map[string]interface{}{
		"enabled": false,
	}

	if httpRoute.Retries != nil {
		retryPolicy["enabled"] = true
		retryPolicy["numRetries"] = int(httpRoute.Retries.Attempts)
		if httpRoute.Retries.RetryOn != "" {
			retryPolicy["retryConditions"] = httpRoute.Retries.RetryOn
		}
	}

	return timeoutPolicy, retryPolicy
}

// extractLoadBalanceConfigWithHashInfo 提取负载均衡配置，包括哈希一致性信息
func (core *APIServerCore) extractLoadBalanceConfigWithHashInfo(
	targetService map[string]interface{},
	routeName string,
	istioClient istioclientset.Interface,
	gatewayNamespace string) {

	serviceNameStr := targetService["serviceName"].(string)
	destinationRuleName := fmt.Sprintf("%s-%s-dr", routeName, serviceNameStr)
	dr, err := istioClient.NetworkingV1alpha3().DestinationRules(gatewayNamespace).Get(
		context.TODO(), destinationRuleName, metav1.GetOptions{},
	)
	if err != nil || dr == nil {
		return
	}

	// 查找对应的subset，负载均衡配置在subset级别
	subsetName := routeName + "-" + serviceNameStr
	var trafficPolicy *istionetworkingv1alpha3.TrafficPolicy

	// 从subsets中查找对应的subset
	for _, subset := range dr.Spec.Subsets {
		if subset.Name == subsetName && subset.TrafficPolicy != nil {
			trafficPolicy = subset.TrafficPolicy
			break
		}
	}

	if trafficPolicy == nil || trafficPolicy.LoadBalancer == nil {
		return
	}

	// 检查是否为哈希一致性负载均衡
	if consistentHash, ok := trafficPolicy.LoadBalancer.LbPolicy.(*istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHash); ok && consistentHash != nil {
		targetService["loadBalanceAlgorithm"] = "consistent-hash"

		// 提取哈希配置
		if consistentHash.ConsistentHash != nil {
			hashConfig := consistentHash.ConsistentHash

			if httpHeaderName, ok := hashConfig.HashKey.(*istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_HttpHeaderName); ok {
				targetService["hashType"] = "header"
				targetService["hashKey"] = httpHeaderName.HttpHeaderName
			} else if queryParam, ok := hashConfig.HashKey.(*istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_HttpQueryParameterName); ok {
				targetService["hashType"] = "query_param"
				targetService["hashKey"] = queryParam.HttpQueryParameterName
			} else if httpCookie, ok := hashConfig.HashKey.(*istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_HttpCookie); ok {
				targetService["hashType"] = "cookie"
				targetService["hashKey"] = httpCookie.HttpCookie.Name
			} else if _, ok := hashConfig.HashKey.(*istionetworkingv1alpha3.LoadBalancerSettings_ConsistentHashLB_UseSourceIp); ok {
				targetService["hashType"] = "ip"
				targetService["hashKey"] = true
			}
		}
	} else if simple, ok := trafficPolicy.LoadBalancer.LbPolicy.(*istionetworkingv1alpha3.LoadBalancerSettings_Simple); ok && simple != nil {
		// 处理普通负载均衡算法
		switch simple.Simple {
		case istionetworkingv1alpha3.LoadBalancerSettings_ROUND_ROBIN:
			targetService["loadBalanceAlgorithm"] = "round-robin"
		case istionetworkingv1alpha3.LoadBalancerSettings_LEAST_CONN:
			targetService["loadBalanceAlgorithm"] = "least-conn"
		case istionetworkingv1alpha3.LoadBalancerSettings_RANDOM:
			targetService["loadBalanceAlgorithm"] = "random"
		}
	}
}

// UpdateRoute 更新路由
func (core *APIServerCore) UpdateRoute(ctx csmContext.CsmContext) error {
	// 获取路径参数 - 实例ID和路由名称
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	routeName := ctx.Param("routeName")
	if routeName == "" {
		return csmErr.NewMissingParametersException("routeName")
	}

	// 获取请求头中的区域信息
	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return csmErr.NewMissingParametersException("X-Region header")
	}

	// 绑定请求参数
	updateRequest := &meta.AIRouteRequest{}

	if err := ctx.Bind(updateRequest); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	ctx.CsmLogger().Infof("开始更新路由: routeName=%s, multiService=%v, srcProduct=%s", routeName, updateRequest.MultiService, updateRequest.SrcProduct)

	// 验证多服务配置
	if err := core.validateMultiServiceConfig(updateRequest); err != nil {
		return err
	}

	// 验证Token限流配置
	if err := core.validateTokenRateLimitConfig(updateRequest); err != nil {
		return err
	}

	// 验证Rewrite配置
	updateRewriteConfig := updateRequest.Rewrite
	ctx.CsmLogger().Infof("更新路由验证：rewrite配置 enabled=%v, path=%s",
		updateRewriteConfig.Enabled, updateRewriteConfig.Path)
	if err := updateRequest.ValidateRewriteConfig(); err != nil {
		ctx.CsmLogger().Errorf("路径重写配置验证失败: %v", err)
		return csmErr.NewInvalidParameterValueException(err.Error())
	}

	// 获取目标网关的命名空间
	gatewayNamespace := "istio-system-" + instanceId

	// 创建集群客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	client, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		return errors.Wrap(err, "Failed to create Istio client")
	}
	istioClient := client.Istio()

	// 获取当前的VirtualService资源
	vs, err := istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).Get(
		context.TODO(), routeName, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return csmErr.NewResourceNotFoundException(fmt.Sprintf("Route %s not found", routeName), err)
		}
		return errors.Wrap(err, "Failed to get VirtualService")
	}

	// 确保使用URL中的routeName而不是请求体中的RouteName
	updateRequest.RouteName = routeName

	// 根据是否为多服务模式创建不同的VirtualService
	var newVirtualService *v1alpha3.VirtualService
	if updateRequest.MultiService {
		newVirtualService, err = core.createMultiServiceVirtualService(ctx, updateRequest, gatewayNamespace)
	} else {
		newVirtualService, err = core.createSingleServiceVirtualService(ctx, updateRequest, gatewayNamespace)
	}

	if err != nil {
		return errors.Wrap(err, "Failed to create VirtualService configuration")
	}

	// 更新现有VirtualService的spec和annotations
	vs.Spec = newVirtualService.Spec

	// 更新annotations
	if vs.Annotations == nil {
		vs.Annotations = make(map[string]string)
	}

	// 复制新的annotations
	for key, value := range newVirtualService.Annotations {
		vs.Annotations[key] = value
	}

	// 删除旧的DestinationRules（如果存在）
	if err := core.deleteOldDestinationRules(ctx, routeName, gatewayNamespace, istioClient); err != nil {
		ctx.CsmLogger().Errorf("删除旧DestinationRule失败: %v", err)
		// 继续执行，不影响主流程
	}

	// 创建新的DestinationRule（如果需要）
	if err := core.createDestinationRulesIfNeeded(ctx, updateRequest, gatewayNamespace, istioClient); err != nil {
		ctx.CsmLogger().Errorf("创建DestinationRule失败: %v", err)
		// 继续执行，不影响主流程
	}

	// 处理模型路由EnvoyFilter
	if updateRequest.MultiService && updateRequest.TrafficDistributionStrategy == "model_name" {
		if err := core.createModelRouteEnvoyFilter(ctx, updateRequest, gatewayNamespace, client); err != nil {
			ctx.CsmLogger().Errorf("创建模型路由EnvoyFilter失败: %v", err)
			// 继续执行，不影响主流程
		}
	} else {
		// 删除旧的EnvoyFilter（如果存在）
		if err := core.deleteModelRouteEnvoyFilter(ctx, routeName, gatewayNamespace, client); err != nil {
			ctx.CsmLogger().Errorf("删除模型路由EnvoyFilter失败: %v", err)
			// 继续执行，不影响主流程
		}
	}

	// 记录更新时间
	updateTime := time.Now().Format("2006-01-02 15:04:05")
	vs.Annotations["update-time"] = updateTime

	// 更新VirtualService资源
	_, err = istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).Update(
		context.TODO(), vs, metav1.UpdateOptions{})
	if err != nil {
		return errors.Wrap(err, "Failed to update VirtualService")
	}

	// 当srcProduct=pom时，将传入的service添加到数据库中
	//if updateRequest.SrcProduct == "pom" {
	//	ctx.CsmLogger().Infof("srcProduct为pom，开始添加服务到数据库")
	//
	//	// 构建服务列表和命名空间信息
	//	var serviceList []string
	//	var namespace string
	//
	//	if updateRequest.MultiService {
	//		// 多服务模式
	//		multiServices, err := updateRequest.GetMultiTargetServices()
	//		if err != nil {
	//			ctx.CsmLogger().Errorf("获取多服务配置失败: %v", err)
	//		} else {
	//			for _, service := range multiServices {
	//				serviceList = append(serviceList, service.ServiceName)
	//				// 假设所有服务在同一个命名空间中，使用第一个服务的命名空间
	//				if namespace == "" {
	//					namespace = service.Namespace
	//				}
	//			}
	//		}
	//	} else {
	//		// 单服务模式
	//		singleService, err := updateRequest.GetSingleTargetService()
	//		if err != nil {
	//			ctx.CsmLogger().Errorf("获取单服务配置失败: %v", err)
	//		} else {
	//			serviceList = append(serviceList, singleService.ServiceName)
	//			namespace = singleService.Namespace
	//		}
	//	}
	//
	//	if len(serviceList) > 0 && namespace != "" {
	//		addServiceRequest := &meta.AddServiceRequest{
	//			ClusterID:     hostedClusterId,
	//			ServiceSource: "CCE",
	//			Namespace:     namespace,
	//			ServiceList:   serviceList,
	//		}
	//
	//		// 调用AddServices方法
	//		_, err := core.aiServiceService.AddServices(ctx, instanceId, addServiceRequest, hostedClusterId)
	//		if err != nil {
	//			ctx.CsmLogger().Errorf("添加服务到数据库失败: %v", err)
	//			// 不影响主流程，继续执行
	//		} else {
	//			ctx.CsmLogger().Infof("成功添加%d个服务到数据库", len(serviceList))
	//		}
	//	}
	//}

	// 处理Token限流配置
	ctx.CsmLogger().Infof("Processing token rate limit update for route %s", routeName)

	// 创建或更新Token限流插件
	dynamicClient := client.Dynamic()
	var tokenRateLimitResponse *meta.TokenRateLimitResponse
	ruleName := fmt.Sprintf("token-limit-%s", strings.ToLower(routeName))

	if updateRequest.TokenRateLimit.Enabled {
		ctx.CsmLogger().Infof("Token rate limit is enabled for route %s", routeName)

		// 验证Token限流配置
		if len(updateRequest.TokenRateLimit.RuleItems) == 0 {
			return csmErr.NewInvalidParameterValueException("Token rate limit is enabled but no rule items provided")
		}

		// 检验每个规则项的合法性
		for i, item := range updateRequest.TokenRateLimit.RuleItems {
			// 当类型为header或query_param时，value字段必填
			if (item.MatchCondition.Type == "header" || item.MatchCondition.Type == "query_param") &&
				item.MatchCondition.Value == "" {
				return csmErr.NewInvalidParameterValueException(
					fmt.Sprintf("Rule item #%d: value is required when type is %s",
						i+1, item.MatchCondition.Type))
			}

			// 验证token数量必须大于0
			if item.LimitConfig.TokenAmount <= 0 {
				return csmErr.NewInvalidParameterValueException(
					fmt.Sprintf("Rule item #%d: token amount must be greater than 0", i+1))
			}
		}

		// 创建或更新Token限流插件
		err := core.createTokenRateLimitPlugin(
			ctx,
			gatewayNamespace,
			routeName,
			&updateRequest.TokenRateLimit,
			client)

		if err != nil {
			ctx.CsmLogger().Errorf("Failed to create/update token rate limit plugin: %v", err)
			// 继续执行，不影响主流程
		} else {
			// 构建响应中的TokenRateLimit信息
			tokenRateLimitResponse = &meta.TokenRateLimitResponse{
				RuleName:  ruleName,
				Enabled:   true,
				RuleItems: updateRequest.TokenRateLimit.RuleItems,
			}
			ctx.CsmLogger().Infof("Token rate limit plugin created/updated successfully for route %s", routeName)
		}
	} else {
		ctx.CsmLogger().Infof("Token rate limit is disabled for route %s, attempting to remove existing rules", routeName)

		// 删除现有的Token限流规则
		err := core.deleteTokenRateLimitRule(ctx, gatewayNamespace, routeName, client)
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to delete token rate limit rule for route %s: %v", routeName, err)
			// 继续执行，不影响主流程
		} else {
			ctx.CsmLogger().Infof("Token rate limit rule deleted successfully for route %s", routeName)
		}

		// 设置响应中的TokenRateLimit信息为已禁用
		tokenRateLimitResponse = &meta.TokenRateLimitResponse{
			Enabled: false,
		}
	}

	// 如果请求中包含 AllowedConsumers 字段（不论是空数组还是有内容），更新消费者的匹配规则
	if updateRequest.AllowedConsumers != nil {
		// 获取并更新 key-auth WasmPlugin
		groupVersionResource := schema.GroupVersionResource{
			Group:    "extensions.higress.io",
			Version:  "v1alpha1",
			Resource: "wasmplugins",
		}
		unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to list WasmPlugins when updating consumers: %v", err)
			// 继续执行，不影响主流程
		} else {
			// 查找 key-auth 插件
			var keyAuthPlugin *unstructured.Unstructured
			for _, item := range unstructuredList.Items {
				name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
				if name == constants.GetKeyAuthPluginName() {
					keyAuthPlugin = &item
					break
				}
			}

			if keyAuthPlugin != nil {
				spec, exists, _ := unstructured.NestedMap(keyAuthPlugin.Object, "spec")
				if !exists {
					spec = map[string]interface{}{}
					keyAuthPlugin.Object["spec"] = spec
				}

				// 获取现有匹配规则
				matchRules, exists, _ := unstructured.NestedSlice(spec, "matchRules")
				if !exists {
					matchRules = []interface{}{}
				}

				// 分为与当前路由相关和无关的规则
				var routeRelatedRules []interface{}
				var otherRules []interface{}

				// 找出所有相关和不相关的规则
				for _, ruleItem := range matchRules {
					rule, ok := ruleItem.(map[string]interface{})
					if !ok {
						continue
					}

					ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
					if !exists {
						otherRules = append(otherRules, rule)
						continue
					}

					// 检查规则是否应用于当前路由
					includesCurrentRoute := false
					for _, ing := range ingressList {
						if routeStr, ok := ing.(string); ok && routeStr == routeName {
							includesCurrentRoute = true
							break
						}
					}

					if includesCurrentRoute {
						// 保存相关规则，稍后会根据认证开关状态处理
						routeRelatedRules = append(routeRelatedRules, rule)
					} else {
						// 保存不相关的规则
						otherRules = append(otherRules, rule)
					}
				}

				// 准备更新后的规则集合
				updatedRules := otherRules

				// 如果启用了认证，处理当前路由相关规则
				if updateRequest.AuthEnabled {
					if len(updateRequest.AllowedConsumers) > 0 {
						// 创建单个规则，包含所有消费者
						newRule := map[string]interface{}{
							"config": map[string]interface{}{
								"allow": []interface{}{},
							},
							"configDisable": false,
							"ingress":       []interface{}{routeName},
						}

						// 添加所有消费者到allow列表
						allowList := []interface{}{}
						for _, consumerName := range updateRequest.AllowedConsumers {
							allowList = append(allowList, consumerName)
						}

						// 设置allow列表
						configMap, _ := newRule["config"].(map[string]interface{})
						configMap["allow"] = allowList

						updatedRules = append(updatedRules, newRule)
					} else if updateRequest.AllowedConsumers != nil {
						// 明确提供了空的AllowedConsumers数组，表示清空消费者
						newRule := map[string]interface{}{
							"config": map[string]interface{}{
								"allow": core.addVirtualConsumerToEmptyAllowArray([]interface{}{}),
							},
							"configDisable": false,
							"ingress":       []interface{}{routeName},
						}
						updatedRules = append(updatedRules, newRule)
					} else if len(routeRelatedRules) > 0 {
						// 没有提供AllowedConsumers字段，保留现有规则
						mergedRule := map[string]interface{}{
							"config": map[string]interface{}{
								"allow": []interface{}{},
							},
							"configDisable": false,
							"ingress":       []interface{}{routeName},
						}

						// 合并所有现有规则的allow列表
						allAllowedConsumers := make(map[string]bool)
						for _, ruleItem := range routeRelatedRules {
							rule, ok := ruleItem.(map[string]interface{})
							if !ok {
								continue
							}

							configMap, exists, _ := unstructured.NestedMap(rule, "config")
							if !exists {
								continue
							}

							allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
							if !exists {
								continue
							}

							for _, allowItem := range allowList {
								if consumerName, ok := allowItem.(string); ok {
									allAllowedConsumers[consumerName] = true
								}
							}
						}

						// 构建去重后的allow列表
						mergedAllowList := []interface{}{}
						for consumerName := range allAllowedConsumers {
							mergedAllowList = append(mergedAllowList, consumerName)
						}

						configMap, _ := mergedRule["config"].(map[string]interface{})
						configMap["allow"] = mergedAllowList
						updatedRules = append(updatedRules, mergedRule)
					}
				} else {
					// 认证关闭时，保留现有规则但禁用
					if len(routeRelatedRules) > 0 {
						// 合并所有现有规则到一个规则
						mergedRule := map[string]interface{}{
							"config": map[string]interface{}{
								"allow": []interface{}{},
							},
							"configDisable": true, // 禁用认证
							"ingress":       []interface{}{routeName},
						}

						// 合并所有现有规则的allow列表
						allAllowedConsumers := make(map[string]bool)
						for _, ruleItem := range routeRelatedRules {
							rule, ok := ruleItem.(map[string]interface{})
							if !ok {
								continue
							}

							configMap, exists, _ := unstructured.NestedMap(rule, "config")
							if !exists {
								continue
							}

							allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
							if !exists {
								continue
							}

							for _, allowItem := range allowList {
								if consumerName, ok := allowItem.(string); ok {
									allAllowedConsumers[consumerName] = true
								}
							}
						}

						// 构建去重后的allow列表
						mergedAllowList := []interface{}{}
						for consumerName := range allAllowedConsumers {
							mergedAllowList = append(mergedAllowList, consumerName)
						}

						configMap, _ := mergedRule["config"].(map[string]interface{})
						configMap["allow"] = mergedAllowList
						updatedRules = append(updatedRules, mergedRule)
					}
				}

				// 更新匹配规则
				if err := unstructured.SetNestedSlice(keyAuthPlugin.Object, updatedRules, "spec", "matchRules"); err != nil {
					ctx.CsmLogger().Errorf("Failed to update matchRules: %v", err)
				} else {
					// 更新 key-auth 插件
					_, err = dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).Update(
						context.TODO(), keyAuthPlugin, metav1.UpdateOptions{})
					if err != nil {
						ctx.CsmLogger().Errorf("Failed to update key-auth plugin: %v", err)
					}
				}
			}
		}
	}

	// 处理AI配额插件的ingress列表
	if updateRequest.AuthEnabled {
		err = core.updateAIQuotaPluginIngress(ctx, gatewayNamespace, routeName, "add", client)
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to update AI Quota plugin ingress: %v", err)
			// 继续执行，不影响主流程
		}
	} else {
		err = core.updateAIQuotaPluginIngress(ctx, gatewayNamespace, routeName, "remove", client)
		if err != nil {
			ctx.CsmLogger().Errorf("Failed to remove AI Quota plugin ingress: %v", err)
			// 继续执行，不影响主流程
		}
	}

	// 返回成功响应
	result := map[string]interface{}{
		"routeName":  routeName,
		"updateTime": updateTime,
	}

	// 添加Token限流信息到响应
	if tokenRateLimitResponse != nil {
		result["tokenRateLimit"] = tokenRateLimitResponse
	}

	return ctx.JSON(http.StatusOK, result)
}

// extractServiceNamesFromVS 从VirtualService中提取服务名称，支持单服务和多服务模式
func (core *APIServerCore) extractServiceNamesFromVS(vs *v1alpha3.VirtualService) []string {
	serviceNames := make([]string, 0)
	serviceNameSet := make(map[string]bool) // 用于去重

	// 遍历所有HTTP路由规则
	for _, httpRoute := range vs.Spec.Http {
		// 遍历每个路由规则的目标
		for _, route := range httpRoute.Route {
			if route.Destination != nil && route.Destination.Host != "" {
				// 从host中提取服务名称
				// host格式通常为: serviceName.namespace.svc.cluster.local
				host := route.Destination.Host
				parts := strings.Split(host, ".")
				if len(parts) >= 1 {
					serviceName := parts[0]
					// 去重
					if !serviceNameSet[serviceName] {
						serviceNames = append(serviceNames, serviceName)
						serviceNameSet[serviceName] = true
					}
				}
			}
		}
	}

	return serviceNames
}

// DeleteRoute 删除路由
func (core *APIServerCore) DeleteRoute(ctx csmContext.CsmContext) error {
	// 获取路径参数 - 实例ID和路由名称
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	routeName := ctx.Param("routeName")
	if routeName == "" {
		return csmErr.NewMissingParametersException("routeName")
	}

	// 获取请求头中的区域信息
	region := ctx.Get(reg.ContextRegion).(string)
	if region == "" {
		return csmErr.NewMissingParametersException("X-Region header")
	}

	// 获取目标网关的命名空间
	gatewayNamespace := "istio-system-" + instanceId

	// 创建集群客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	client, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		return errors.Wrap(err, "Failed to create Istio client")
	}
	istioClient := client.Istio()

	// 获取指定的VirtualService资源，先检查是否存在并获取服务信息
	vs, err := istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).Get(
		context.TODO(), routeName, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return csmErr.NewResourceNotFoundException(fmt.Sprintf("Route %s not found", routeName), err)
		}
		return errors.Wrap(err, "Failed to get VirtualService")
	}

	// 从VirtualService中提取服务名称，用于删除关联的DestinationRule
	serviceNames := core.extractServiceNamesFromVS(vs)
	ctx.CsmLogger().Infof("Extracted service names from VirtualService %s: %v", routeName, serviceNames)

	// 删除VirtualService资源
	err = istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).Delete(
		context.TODO(), routeName, metav1.DeleteOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return csmErr.NewResourceNotFoundException(fmt.Sprintf("Route %s not found", routeName), err)
		}
		return errors.Wrap(err, "Failed to delete VirtualService")
	}

	// 尝试删除关联的DestinationRule（支持单服务和多服务模式）
	for _, serviceName := range serviceNames {
		destinationRuleName := fmt.Sprintf("%s-%s-dr", routeName, serviceName)
		ctx.CsmLogger().Infof("Attempting to delete DestinationRule: %s", destinationRuleName)

		err = istioClient.NetworkingV1alpha3().DestinationRules(gatewayNamespace).Delete(
			context.TODO(), destinationRuleName, metav1.DeleteOptions{})
		if err != nil && !k8serrors.IsNotFound(err) {
			ctx.CsmLogger().Errorf("Failed to delete DestinationRule %s: %v", destinationRuleName, err)
			// 继续执行，不影响主流程
		} else if err == nil {
			ctx.CsmLogger().Infof("Successfully deleted DestinationRule: %s", destinationRuleName)
		}
	}

	// 更新key-auth WasmPlugin中的matchRules，删除与该路由相关的规则
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := client.Dynamic()
	unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to list WasmPlugins when cleaning up matchRules: %v", err)
		// 记录错误但继续执行，不影响主流程
	} else {
		// 查找key-auth插件
		var keyAuthPlugin *unstructured.Unstructured
		for _, item := range unstructuredList.Items {
			name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
			if name == constants.GetKeyAuthPluginName() {
				keyAuthPlugin = &item
				break
			}
		}

		if keyAuthPlugin != nil {
			// 获取并更新matchRules
			spec, exists, _ := unstructured.NestedMap(keyAuthPlugin.Object, "spec")
			if exists {
				matchRules, exists, _ := unstructured.NestedSlice(spec, "matchRules")
				if exists {
					updatedMatchRules := []interface{}{}

					// 遍历所有规则，排除与当前路由相关的规则
					for _, ruleItem := range matchRules {
						rule, ok := ruleItem.(map[string]interface{})
						if !ok {
							continue
						}

						ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
						if !exists {
							// 保留没有ingress字段的规则
							updatedMatchRules = append(updatedMatchRules, rule)
							continue
						}

						// 检查ingress列表中是否包含要删除的路由
						keepRule := true
						for _, ingressItem := range ingressList {
							if routeValue, ok := ingressItem.(string); ok && routeValue == routeName {
								keepRule = false
								break
							}
						}

						if keepRule {
							updatedMatchRules = append(updatedMatchRules, rule)
						}
					}

					// 更新matchRules
					err := unstructured.SetNestedSlice(keyAuthPlugin.Object, updatedMatchRules, "spec", "matchRules")
					if err != nil {
						ctx.CsmLogger().Errorf("Failed to update matchRules: %v", err)
					} else {
						// 保存更新后的WasmPlugin
						_, err = dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).Update(
							context.TODO(), keyAuthPlugin, metav1.UpdateOptions{})
						if err != nil {
							ctx.CsmLogger().Errorf("Failed to update key-auth plugin: %v", err)
						}
					}
				}
			}
		}
	}

	// 删除ai-token-limit插件中的限流规则
	err = core.deleteTokenRateLimitRule(ctx, gatewayNamespace, routeName, client)
	if err != nil {
		ctx.CsmLogger().Errorf("删除Token限流规则时发生错误: %v", err)
		// 记录错误但继续执行，不影响主流程
	}

	// 从AI配额插件的ingress列表中移除路由
	err = core.updateAIQuotaPluginIngress(ctx, gatewayNamespace, routeName, "remove", client)
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to remove route from AI Quota plugin ingress: %v", err)
		// 继续执行，不影响主流程
	}

	// 获取当前时间作为删除时间
	deletedTime := time.Now().Format("2006-01-02 15:04:05")

	// 返回成功响应，只包含result部分
	result := map[string]interface{}{
		"routeName":   routeName,
		"deletedTime": deletedTime,
	}

	return ctx.JSON(http.StatusOK, result)
}

func generateUUID() string {
	var letters = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
	b := make([]rune, 12)
	rand.Seed(time.Now().UnixNano())
	for j := range b {
		b[j] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

// CreateConsumer 创建消费者
func (core *APIServerCore) CreateConsumer(ctx csmContext.CsmContext) error {
	// 获取实例ID
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}

	// 绑定请求参数
	createConsumerReq := &meta.CreateConsumerRequest{}
	if err := ctx.Bind(createConsumerReq); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 验证请求参数
	if err := createConsumerReq.Validate(); err != nil {
		return csmErr.NewInvalidParameterValueException(err.Error())
	}

	region := ctx.Get(reg.ContextRegion).(string)
	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create cluster client: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)
	ctx.CsmLogger().Infof("creating consumer for gateway instance %s in namespace %s", instanceId, namespace)

	// 生成消费者ID，使用时间戳和随机数
	uuid := generateUUID()
	consumerId := fmt.Sprintf("cs-%s", uuid)

	// 生成唯一凭证，格式类似 f881a93b-678b4177979-a4f4b7270ada
	var b [16]byte
	if _, err := rand.Read(b[:]); err != nil {
		return errors.Wrap(err, "failed to generate credential")
	}
	// 将 16 字节随机数分为三段：4/6/6 字节，分别 hex 编码
	segment1 := hex.EncodeToString(b[:4])
	segment2 := hex.EncodeToString(b[4:10])
	segment3 := hex.EncodeToString(b[10:16])
	credential := fmt.Sprintf("Bearer %s-%s-%s", segment1, segment2, segment3)

	// 使用dynamic client获取WasmPlugin资源
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := hostingClient.Dynamic()

	// 创建消费者的时间，格式化为北京时间字符串
	currentTime := time.Now().In(time.FixedZone("CST", 8*60*60))
	createTime := currentTime.Format("2006-01-02 15:04:05")

	// 查询key-auth WasmPlugin
	var keyAuthPlugin *unstructured.Unstructured
	unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to list WasmPlugins: %v", err)
		return err
	}

	for _, item := range unstructuredList.Items {
		name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
		if name == constants.GetKeyAuthPluginName() {
			keyAuthPlugin = &item
			break
		}
	}

	if keyAuthPlugin == nil {
		// 如果不存在key-auth插件，先创建基础插件，然后添加消费者
		ctx.CsmLogger().Infof("Key Auth插件不存在，正在创建基础插件并添加消费者")

		// 创建基础Key Auth插件
		err := core.createBaseKeyAuthPlugin(ctx, namespace, hostingClient)
		if err != nil {
			ctx.CsmLogger().Errorf("创建基础Key Auth插件失败: %v", err)
			return errors.Wrap(err, "failed to create base Key Auth plugin")
		}

		// 重新查询插件
		unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("重新查询WasmPlugins失败: %v", err)
			return errors.Wrap(err, "failed to re-query WasmPlugins")
		}

		for _, item := range unstructuredList.Items {
			name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
			if name == constants.GetKeyAuthPluginName() {
				keyAuthPlugin = &item
				break
			}
		}

		if keyAuthPlugin == nil {
			return errors.New("创建Key Auth插件后仍然无法找到插件")
		}
	}

	//添加新的consumer
	spec, exists, _ := unstructured.NestedMap(keyAuthPlugin.Object, "spec")
	if !exists {
		return errors.New("existing key-auth plugin has no spec field")
	}

	defaultConfig, exists, _ := unstructured.NestedMap(spec, "defaultConfig")
	if !exists {
		return errors.New("existing key-auth plugin has no defaultConfig field")
	}

	consumersData, exists, _ := unstructured.NestedSlice(defaultConfig, "consumers")
	if !exists {
		consumersData = []interface{}{}
	}

	// 确保虚拟消费者存在
	consumersData = core.ensureVirtualConsumerExists(consumersData)

	// 检查消费者名称是否已存在
	for _, consumerItem := range consumersData {
		consumer, ok := consumerItem.(map[string]interface{})
		if !ok {
			continue
		}

		consumerName, _, _ := unstructured.NestedString(consumer, "name")
		if consumerName == createConsumerReq.ConsumerName {
			return csmErr.NewInvalidParameterValueException("消费者名称已存在")
		}
	}

	// 添加新的consumer
	newConsumer := map[string]interface{}{
		"credential":     credential,
		"name":           createConsumerReq.ConsumerName,
		"id":             consumerId,
		"description":    createConsumerReq.Description,
		"authType":       createConsumerReq.AuthType,
		"createTime":     createTime,
		"unlimitedQuota": createConsumerReq.UnlimitedQuota, // 配额管理字段
		"srcProduct":     createConsumerReq.SrcProduct,     // 新增：来源产品字段
	}

	// 添加totalQuota字段到插件中
	if !createConsumerReq.UnlimitedQuota && createConsumerReq.TotalQuota != nil {
		// 确保类型一致性，转换为int64以避免深拷贝时的类型冲突
		newConsumer["totalQuota"] = int64(*createConsumerReq.TotalQuota)
	}

	consumersData = append(consumersData, newConsumer)

	// 更新consumers列表
	err = unstructured.SetNestedSlice(keyAuthPlugin.Object, consumersData, "spec", "defaultConfig", "consumers")
	if err != nil {
		return err
	}

	// 新增：设置Redis中的配额值（剩余配额）
	ctx.CsmLogger().Infof("开始设置消费者 %s 的配额信息", createConsumerReq.ConsumerName)
	err = core.setConsumerQuota(ctx, instanceId, createConsumerReq.ConsumerName, createConsumerReq.UnlimitedQuota, createConsumerReq.TotalQuota)
	if err != nil {
		// 配额设置失败不影响消费者创建，但需要记录错误日志
		ctx.CsmLogger().Errorf("设置消费者配额失败，但消费者创建成功: %v", err)
	} else {
		ctx.CsmLogger().Infof("成功设置消费者 %s 的配额信息", createConsumerReq.ConsumerName)
	}

	// 如果有routeNames，更新或添加matchRules
	if len(createConsumerReq.RouteNames) > 0 {
		matchRules, exists, _ := unstructured.NestedSlice(spec, "matchRules")
		if !exists {
			matchRules = []interface{}{}
		}

		// 创建路由ID到规则的映射
		routeRulesMap := make(map[string]map[string]interface{})
		updatedRoutes := make(map[string]bool)

		// 首先收集现有规则
		for _, ruleItem := range matchRules {
			rule, ok := ruleItem.(map[string]interface{})
			if !ok {
				continue
			}

			ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
			if !exists || len(ingressList) == 0 {
				continue
			}

			// 假设每个规则只有一个路由
			routeName, ok := ingressList[0].(string)
			if !ok {
				continue
			}

			// 保存路由规则映射
			routeRulesMap[routeName] = rule
		}

		// 处理新的路由权限
		newMatchRules := []interface{}{}

		// 先处理需要更新的路由规则
		for _, routeName := range createConsumerReq.RouteNames {
			updatedRoutes[routeName] = true

			if existingRule, exists := routeRulesMap[routeName]; exists {
				// 更新现有规则
				configMap, exists, _ := unstructured.NestedMap(existingRule, "config")
				if !exists {
					configMap = map[string]interface{}{}
				}

				allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
				if !exists {
					allowList = []interface{}{}
				}

				// 确保不重复添加消费者
				consumerExists := false
				for _, allowItem := range allowList {
					if consumerName, ok := allowItem.(string); ok && consumerName == createConsumerReq.ConsumerName {
						consumerExists = true
						break
					}
				}

				if !consumerExists {
					allowList = append(allowList, createConsumerReq.ConsumerName)
					_ = unstructured.SetNestedSlice(configMap, allowList, "allow")
					_ = unstructured.SetNestedMap(existingRule, configMap, "config")
				}

				newMatchRules = append(newMatchRules, existingRule)
			} else {
				// 创建新规则
				newRule := map[string]interface{}{
					"config": map[string]interface{}{
						"allow": []interface{}{createConsumerReq.ConsumerName},
					},
					"configDisable": false,
					"ingress":       []interface{}{routeName},
				}
				newMatchRules = append(newMatchRules, newRule)
			}
		}

		// 添加未修改的路由规则
		for routeName, rule := range routeRulesMap {
			if !updatedRoutes[routeName] {
				newMatchRules = append(newMatchRules, rule)
			}
		}

		// 更新matchRules
		err := unstructured.SetNestedSlice(keyAuthPlugin.Object, newMatchRules, "spec", "matchRules")
		if err != nil {
			return err
		}
	}

	// 更新key-auth插件
	_, err = dynamicClient.Resource(groupVersionResource).Namespace(namespace).Update(context.TODO(), keyAuthPlugin, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to update key-auth plugin: %v", err)
		return err
	}

	// 返回成功响应，包含consumerId和credential
	response := &meta.CreateConsumerResult{
		Success:    true,
		Status:     http.StatusOK,
		ConsumerID: consumerId,
		Credential: credential,
	}
	return ctx.JSON(http.StatusOK, response)
}

// ListConsumers 查询消费者列表
func (core *APIServerCore) ListConsumers(ctx csmContext.CsmContext) error {
	// 获取实例ID
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}

	region := ctx.Get(reg.ContextRegion).(string)
	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create cluster client: %v", err)
		return err
	}

	// 使用固定的命名空间（在实际生产中，这应该从实例中获取）
	namespace := fmt.Sprintf("istio-system-%s", instanceId)
	ctx.CsmLogger().Infof("listing consumers for gateway instance %s from namespace %s", instanceId, namespace)

	// 使用dynamic client获取WasmPlugin资源
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}
	dynamicClient := hostingClient.Dynamic()
	unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to list WasmPlugins: %v", err)
		return err
	}

	// 从所有WasmPlugin中提取消费者信息
	type Consumer struct {
		ConsumerId     string `json:"consumerId"`
		ConsumerName   string `json:"consumerName"`
		Description    string `json:"description"`
		CreateTime     string `json:"createTime"`
		UnlimitedQuota bool   `json:"unlimitedQuota"` // 是否无限配额
		TotalQuota     int64  `json:"totalQuota"`     // 原始配额值
		QuotaValue     int64  `json:"quotaValue"`     // 当前剩余配额
		SrcProduct     string `json:"srcProduct"`     // 新增：来源产品
	}
	var consumers []Consumer

	for _, item := range unstructuredList.Items {
		// 检查是否为key-auth插件
		name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
		if name != constants.GetKeyAuthPluginName() {
			continue
		}

		// 获取消费者列表
		spec, exists, _ := unstructured.NestedMap(item.Object, "spec")
		if !exists {
			continue
		}

		defaultConfig, exists, _ := unstructured.NestedMap(spec, "defaultConfig")
		if !exists {
			continue
		}

		consumersData, exists, _ := unstructured.NestedSlice(defaultConfig, "consumers")
		if !exists {
			continue
		}

		// 获取插件创建时间作为兜底
		creationTimestamp, exists, _ := unstructured.NestedString(item.Object, "metadata", "creationTimestamp")
		defaultCreateTime := ""
		if exists {
			// 解析时间为UTC时间
			t, err := time.Parse(time.RFC3339, creationTimestamp)
			if err != nil {
				ctx.CsmLogger().Errorf("failed to parse creationTimestamp: %v", err)
				continue
			}

			// 将UTC时间转换为北京时间（东八区）
			beijingTime := t.In(time.FixedZone("CST", 8*60*60))

			// 格式化时间为指定格式
			defaultCreateTime = beijingTime.Format("2006-01-02 15:04:05")
		}

		for _, consumerItem := range consumersData {
			consumer, ok := consumerItem.(map[string]interface{})
			if !ok {
				continue
			}

			// 提取消费者信息
			id, _, _ := unstructured.NestedString(consumer, "id")
			consumerName, _, _ := unstructured.NestedString(consumer, "name")
			description, _, _ := unstructured.NestedString(consumer, "description")
			authType, _, _ := unstructured.NestedString(consumer, "authType")
			srcProduct, _, _ := unstructured.NestedString(consumer, "srcProduct")

			// 过滤掉虚拟消费者
			if core.isVirtualConsumer(consumerName) {
				continue
			}

			// 新增：过滤掉authType=JWT的消费者，不在列表中展示
			if authType == "JWT" {
				ctx.CsmLogger().Infof("过滤JWT类型消费者: %s", consumerName)
				continue
			}

			// 优先从consumer对象中获取createTime
			createTime, exists, _ := unstructured.NestedString(consumer, "createTime")
			if !exists || createTime == "" {
				// 如果不存在，则使用插件的创建时间作为兜底
				createTime = defaultCreateTime
			}

			// 新增：获取配额信息
			unlimitedQuota, _, _ := unstructured.NestedBool(consumer, "unlimitedQuota")
			var totalQuota int64 = 0  // 原始配额值
			var quotaValue int64 = -1 // 剩余配额值

			if unlimitedQuota {
				totalQuota = -1 // 无限配额的原始配额为-1
				quotaValue = -1 // 无限配额的剩余配额为-1
			} else {
				// 从插件获取原始配额值，尝试多种类型
				if totalQuotaInt64, exists, _ := unstructured.NestedInt64(consumer, "totalQuota"); exists {
					totalQuota = totalQuotaInt64
					ctx.CsmLogger().Infof("从插件获取消费者 %s 的totalQuota(int64): %d", consumerName, totalQuota)
				} else if totalQuotaFloat64, exists, _ := unstructured.NestedFloat64(consumer, "totalQuota"); exists {
					totalQuota = int64(totalQuotaFloat64)
					ctx.CsmLogger().Infof("从插件获取消费者 %s 的totalQuota(float64): %d", consumerName, totalQuota)
				} else if totalQuotaStr, exists, _ := unstructured.NestedString(consumer, "totalQuota"); exists {
					if totalQuotaInt, err := strconv.ParseInt(totalQuotaStr, 10, 64); err == nil {
						totalQuota = totalQuotaInt
						ctx.CsmLogger().Infof("从插件获取消费者 %s 的totalQuota(string): %d", consumerName, totalQuota)
					} else {
						ctx.CsmLogger().Warnf("消费者 %s 的totalQuota字段格式错误: %s", consumerName, totalQuotaStr)
						totalQuota = 0
					}
				} else {
					ctx.CsmLogger().Warnf("消费者 %s 在插件配置中没有totalQuota字段", consumerName)
					// 如果插件配置中没有totalQuota字段，说明是旧版本创建的消费者
					// 这种情况下，我们无法确定原始配额值，只能设为0
					totalQuota = 0
				}

				// 从Redis获取当前剩余配额
				currentQuota, err := core.getConsumerQuota(ctx, instanceId, consumerName)
				if err != nil {
					ctx.CsmLogger().Warnf("获取消费者 %s 配额失败: %v", consumerName, err)
					quotaValue = 0 // 获取失败时返回0
				} else {
					quotaValue = currentQuota
					ctx.CsmLogger().Infof("从Redis获取消费者 %s 的剩余配额: %d", consumerName, quotaValue)
				}
			}

			// 添加到消费者列表
			consumers = append(consumers, Consumer{
				ConsumerId:     id,
				ConsumerName:   consumerName,
				Description:    description,
				CreateTime:     createTime,
				UnlimitedQuota: unlimitedQuota,
				TotalQuota:     totalQuota,
				QuotaValue:     quotaValue,
				SrcProduct:     srcProduct,
			})
		}
	}

	// 获取分页参数
	pageNo := 1
	pageSize := 10
	orderBy := "createTime"
	order := "desc"

	// 从查询参数中获取分页信息
	if pageNoStr := ctx.QueryParam("pageNo"); pageNoStr != "" {
		if val, err := strconv.Atoi(pageNoStr); err == nil && val > 0 {
			pageNo = val
		}
	}
	if pageSizeStr := ctx.QueryParam("pageSize"); pageSizeStr != "" {
		if val, err := strconv.Atoi(pageSizeStr); err == nil && val > 0 {
			pageSize = val
		}
	}
	if orderByStr := ctx.QueryParam("orderBy"); orderByStr != "" {
		orderBy = orderByStr
	}
	if orderStr := ctx.QueryParam("order"); orderStr != "" {
		order = orderStr
	}

	// 根据排序参数对消费者列表进行排序
	if len(consumers) > 0 {
		sort.Slice(consumers, func(i, j int) bool {
			var result bool
			switch orderBy {
			case "createTime":
				// 将时间字符串解析为time.Time对象进行比较
				iTime, errI := time.Parse("2006-01-02 15:04:05", consumers[i].CreateTime)
				jTime, errJ := time.Parse("2006-01-02 15:04:05", consumers[j].CreateTime)

				// 如果解析出错，则使用字符串比较
				if errI != nil || errJ != nil {
					result = consumers[i].CreateTime < consumers[j].CreateTime
				} else {
					result = iTime.After(jTime)
				}
			case "consumerName":
				result = consumers[i].ConsumerName > consumers[j].ConsumerName
			case "description":
				result = consumers[i].Description > consumers[j].Description
			default:
				// 默认按创建时间排序
				iTime, errI := time.Parse("2006-01-02 15:04:05", consumers[i].CreateTime)
				jTime, errJ := time.Parse("2006-01-02 15:04:05", consumers[j].CreateTime)

				if errI != nil || errJ != nil {
					result = consumers[i].CreateTime > consumers[j].CreateTime
				} else {
					result = iTime.After(jTime)
				}
			}

			// 根据order参数决定是升序还是降序
			if order == "asc" {
				return !result
			}
			return result
		})
	}

	// 构建分页响应
	pageResponse := struct {
		OrderBy    string      `json:"orderBy"`
		Order      string      `json:"order"`
		PageNo     int         `json:"pageNo"`
		PageSize   int         `json:"pageSize"`
		TotalCount int         `json:"totalCount"`
		Result     interface{} `json:"result"`
	}{
		OrderBy:    orderBy,
		Order:      order,
		PageNo:     pageNo,
		PageSize:   pageSize,
		TotalCount: len(consumers),
		Result:     consumers,
	}

	// 确保consumers不为nil，如果为nil则设置为空切片
	if consumers == nil {
		pageResponse.Result = []Consumer{}
	}

	return ctx.JSON(http.StatusOK, pageResponse)
}

// GetConsumerDetail 查询消费者详情
func (core *APIServerCore) GetConsumerDetail(ctx csmContext.CsmContext) error {
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}
	consumerId := ctx.Param("ConsumerID")
	if consumerId == "" {
		return csmErr.NewMissingParametersException("ConsumerID")
	}

	region := ctx.Get(reg.ContextRegion).(string)
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create cluster client: %v", err)
		return err
	}

	namespace := fmt.Sprintf("istio-system-%s", instanceId)
	ctx.CsmLogger().Infof("getting consumer detail for instance %s, consumer %s from namespace %s", instanceId, consumerId, namespace)

	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := hostingClient.Dynamic()
	unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to list WasmPlugins: %v", err)
		return err
	}

	consumerDetail, found, findErr := getConsumerDetailFromList(core, ctx, unstructuredList.Items, region, consumerId, hostingClient)
	if findErr != nil {
		return findErr
	}
	if !found {
		return csmErr.NewResourceNotFoundException(fmt.Sprintf("Consumer %s not found", consumerId))
	}

	response := meta.ConsumerDetailResponse{
		Result: *consumerDetail,
	}
	return ctx.JSON(http.StatusOK, response.Result)
}

// 提取消费者详情
func getConsumerDetailFromList(
	core *APIServerCore,
	ctx csmContext.CsmContext,
	items []unstructured.Unstructured,
	region, consumerId string,
	client kube.Client,
) (*meta.ConsumerDetailInfo, bool, error) {
	for _, item := range items {
		name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
		if name != constants.GetKeyAuthPluginName() {
			continue
		}
		spec, exists, _ := unstructured.NestedMap(item.Object, "spec")
		if !exists {
			continue
		}
		defaultConfig, exists, _ := unstructured.NestedMap(spec, "defaultConfig")
		if !exists {
			continue
		}
		consumersData, exists, _ := unstructured.NestedSlice(defaultConfig, "consumers")
		if !exists {
			continue
		}
		creationTimestamp, exists, _ := unstructured.NestedString(item.Object, "metadata", "creationTimestamp")
		defaultCreateTime := ""
		if exists {
			t, err := time.Parse(time.RFC3339, creationTimestamp)
			if err == nil {
				beijingTime := t.In(time.FixedZone("CST", 8*60*60))
				defaultCreateTime = beijingTime.Format("2006-01-02 15:04:05")
			}
		}

		for _, consumerItem := range consumersData {
			consumer, ok := consumerItem.(map[string]interface{})
			if !ok {
				continue
			}
			id, _, _ := unstructured.NestedString(consumer, "id")
			if id != consumerId {
				continue
			}
			consumerName, _, _ := unstructured.NestedString(consumer, "name")

			// 过滤掉虚拟消费者
			if core.isVirtualConsumer(consumerName) {
				continue
			}

			description, _, _ := unstructured.NestedString(consumer, "description")
			authType, _, _ := unstructured.NestedString(consumer, "authType")
			if authType == "" {
				authType = "JWT"
			}
			credential, _, _ := unstructured.NestedString(consumer, "credential")
			srcProduct, _, _ := unstructured.NestedString(consumer, "srcProduct")

			// 优先从consumer对象中获取createTime，如果不存在则使用插件的创建时间
			createTime, exists, _ := unstructured.NestedString(consumer, "createTime")
			if !exists || createTime == "" {
				createTime = defaultCreateTime
			}

			routes, err := extractRoutes(core, ctx, spec, region, consumerName, client)
			if err != nil {
				return nil, false, err
			}

			// 新增：获取配额信息
			unlimitedQuota, _, _ := unstructured.NestedBool(consumer, "unlimitedQuota")
			var totalQuota int64 = 0  // 原始配额值
			var quotaValue int64 = -1 // 剩余配额值

			if unlimitedQuota {
				totalQuota = -1 // 无限配额的原始配额为-1
				quotaValue = -1 // 无限配额的剩余配额为-1
			} else {
				// 从插件获取原始配额值，尝试多种类型
				if totalQuotaInt64, exists, _ := unstructured.NestedInt64(consumer, "totalQuota"); exists {
					totalQuota = totalQuotaInt64
					ctx.CsmLogger().Infof("从插件获取消费者 %s 的totalQuota(int64): %d", consumerName, totalQuota)
				} else if totalQuotaFloat64, exists, _ := unstructured.NestedFloat64(consumer, "totalQuota"); exists {
					totalQuota = int64(totalQuotaFloat64)
					ctx.CsmLogger().Infof("从插件获取消费者 %s 的totalQuota(float64): %d", consumerName, totalQuota)
				} else if totalQuotaStr, exists, _ := unstructured.NestedString(consumer, "totalQuota"); exists {
					if totalQuotaInt, err := strconv.ParseInt(totalQuotaStr, 10, 64); err == nil {
						totalQuota = totalQuotaInt
						ctx.CsmLogger().Infof("从插件获取消费者 %s 的totalQuota(string): %d", consumerName, totalQuota)
					} else {
						ctx.CsmLogger().Warnf("消费者 %s 的totalQuota字段格式错误: %s", consumerName, totalQuotaStr)
						totalQuota = 0
					}
				} else {
					ctx.CsmLogger().Warnf("消费者 %s 在插件配置中没有totalQuota字段", consumerName)
					// 如果插件配置中没有totalQuota字段，说明是旧版本创建的消费者
					// 这种情况下，我们无法确定原始配额值，只能设为0
					totalQuota = 0
				}

				// 从Redis获取当前剩余配额
				currentQuota, err := core.getConsumerQuota(ctx, ctx.Param("InstanceId"), consumerName)
				if err != nil {
					ctx.CsmLogger().Warnf("获取消费者 %s 配额失败: %v", consumerName, err)
					quotaValue = 0 // 获取失败时返回0
				} else {
					quotaValue = currentQuota
					ctx.CsmLogger().Infof("从Redis获取消费者 %s 的剩余配额: %d", consumerName, quotaValue)
				}
			}

			return &meta.ConsumerDetailInfo{
				ConsumerID:     id,
				ConsumerName:   consumerName,
				Description:    description,
				AuthType:       authType,
				AuthInfo:       meta.ConsumerAuthInfo{Token: credential},
				Routes:         routes,
				UnlimitedQuota: unlimitedQuota,
				TotalQuota:     totalQuota,
				QuotaValue:     quotaValue,
				SrcProduct:     srcProduct,
			}, true, nil
		}
	}
	return nil, false, nil
}

// 提取消费者相关路由
func extractRoutes(
	core *APIServerCore,
	ctx csmContext.CsmContext,
	spec map[string]interface{},
	region, consumerName string,
	client kube.Client,
) ([]meta.ConsumerRouteInfo, error) {
	var routes []meta.ConsumerRouteInfo
	matchRules, exists, _ := unstructured.NestedSlice(spec, "matchRules")
	if !exists {
		return routes, nil
	}

	// 提取命名空间 - 从context或请求参数中获取
	instanceId := ctx.Param("InstanceId")
	namespace := fmt.Sprintf("istio-system-%s", instanceId)

	for _, ruleItem := range matchRules {
		rule, ok := ruleItem.(map[string]interface{})
		if !ok {
			continue
		}
		configMap, exists, _ := unstructured.NestedMap(rule, "config")
		if !exists {
			continue
		}
		allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
		if !exists {
			continue
		}
		// 直接在这里判断allow名单
		allowed := false
		for _, allowItem := range allowList {
			if allowName, ok := allowItem.(string); ok && allowName == consumerName {
				allowed = true
				break
			}
		}
		if !allowed {
			continue
		}

		// 获取configDisable值，默认为false（即启用）
		configDisable := false
		configDisableVal, exists, _ := unstructured.NestedBool(rule, "configDisable")
		if exists {
			configDisable = configDisableVal
		}

		ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
		if !exists {
			continue
		}

		istioClient := client.Istio()
		for _, ingressItem := range ingressList {
			if routeName, ok := ingressItem.(string); ok {
				vs, err := istioClient.NetworkingV1alpha3().VirtualServices(namespace).Get(
					context.TODO(), routeName, metav1.GetOptions{})
				if err != nil {
					if k8serrors.IsNotFound(err) {
						return nil, csmErr.NewResourceNotFoundException(fmt.Sprintf("Route %s not found", routeName), err)
					}
					return nil, errors.Wrap(err, "Failed to get VirtualService")
				}
				routes = append(routes, meta.ConsumerRouteInfo{
					RouteName:   routeName,
					CreateTime:  vs.CreationTimestamp.Format("2006-01-02 15:04:05"),
					AuthEnabled: !configDisable, // 当configDisable为false时，AuthEnabled为true
				})
			}
		}
	}
	return routes, nil
}

// UpdateConsumer 编辑消费者信息
func (core *APIServerCore) UpdateConsumer(ctx csmContext.CsmContext) error {
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}
	consumerId := ctx.Param("ConsumerID")
	if consumerId == "" {
		return csmErr.NewMissingParametersException("ConsumerID")
	}
	updateReq := &meta.UpdateConsumerRequest{}
	if err := ctx.Bind(updateReq); err != nil {
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 新增：参数验证
	if err := updateReq.Validate(); err != nil {
		return csmErr.NewInvalidParameterValueException(err.Error())
	}

	region := ctx.Get(reg.ContextRegion).(string)
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create cluster client: %v", err)
		return err
	}
	namespace := fmt.Sprintf("istio-system-%s", instanceId)
	ctx.CsmLogger().Infof("updating consumer for instance %s, consumer %s in namespace %s", instanceId, consumerId, namespace)

	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}
	dynamicClient := hostingClient.Dynamic()
	unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to list WasmPlugins: %v", err)
		return err
	}

	keyAuthPlugin, consumerName, found, updateErr := updateConsumerInList(unstructuredList.Items, consumerId, updateReq)
	if updateErr != nil {
		return updateErr
	}
	if !found {
		return csmErr.NewResourceNotFoundException(fmt.Sprintf("Consumer %s not found", consumerId))
	}

	if updateReq.RouteNames != nil && keyAuthPlugin != nil {
		if err := core.updateConsumerRoutes(keyAuthPlugin, consumerName, updateReq.RouteNames); err != nil {
			return err
		}
	}

	_, err = dynamicClient.Resource(groupVersionResource).Namespace(namespace).Update(context.TODO(), keyAuthPlugin, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to update key-auth plugin: %v", err)
		return err
	}

	// 新增：更新配额信息
	if updateReq.UnlimitedQuota != nil {
		ctx.CsmLogger().Infof("开始更新消费者 %s 的配额信息", consumerName)
		err = core.setConsumerQuota(ctx, instanceId, consumerName, *updateReq.UnlimitedQuota, updateReq.TotalQuota)
		if err != nil {
			// 配额更新失败不影响消费者编辑，但需要记录错误日志
			ctx.CsmLogger().Errorf("更新消费者配额失败，但消费者编辑成功: %v", err)
		} else {
			ctx.CsmLogger().Infof("成功更新消费者 %s 的配额信息", consumerName)
		}
	}

	response := meta.UpdateConsumerResponse{
		Success: true,
		Result:  nil,
		Status:  http.StatusOK,
	}
	return ctx.JSON(http.StatusOK, response)
}

// 查找并更新消费者的描述，返回plugin和consumerName
func updateConsumerInList(
	items []unstructured.Unstructured,
	consumerId string,
	updateReq *meta.UpdateConsumerRequest,
) (
	*unstructured.Unstructured,
	string,
	bool,
	error,
) {
	for _, item := range items {
		name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
		if name != constants.GetKeyAuthPluginName() {
			continue
		}
		spec, exists, _ := unstructured.NestedMap(item.Object, "spec")
		if !exists {
			continue
		}
		defaultConfig, exists, _ := unstructured.NestedMap(spec, "defaultConfig")
		if !exists {
			continue
		}
		consumersData, exists, _ := unstructured.NestedSlice(defaultConfig, "consumers")
		if !exists {
			continue
		}
		for i, consumerItem := range consumersData {
			consumer, ok := consumerItem.(map[string]interface{})
			if !ok {
				continue
			}
			id, _, _ := unstructured.NestedString(consumer, "id")
			if id != consumerId {
				continue
			}
			// 更新描述时保留原有的createTime字段
			if updateReq.Description != "" {
				consumer["description"] = updateReq.Description
			}

			// 新增：更新配额字段
			if updateReq.UnlimitedQuota != nil {
				consumer["unlimitedQuota"] = *updateReq.UnlimitedQuota

				// 如果设置为限制配额且提供了totalQuota，则更新totalQuota字段
				if !*updateReq.UnlimitedQuota && updateReq.TotalQuota != nil {
					consumer["totalQuota"] = int64(*updateReq.TotalQuota)
				} else if *updateReq.UnlimitedQuota {
					// 如果设置为无限配额，移除totalQuota字段
					delete(consumer, "totalQuota")
				}
			}

			consumerName, _, _ := unstructured.NestedString(consumer, "name")
			// 确保createTime字段存在，如果不存在则添加当前时间
			_, createTimeExists, _ := unstructured.NestedString(consumer, "createTime")
			if !createTimeExists {
				currentTime := time.Now().In(time.FixedZone("CST", 8*60*60))
				consumer["createTime"] = currentTime.Format("2006-01-02 15:04:05")
			}
			consumersData[i] = consumer
			err := unstructured.SetNestedSlice(item.Object, consumersData, "spec", "defaultConfig", "consumers")
			if err != nil {
				return nil, "", false, err
			}
			return &item, consumerName, true, nil
		}
	}
	return nil, "", false, nil
}

// 路由规则更新
func (core *APIServerCore) updateConsumerRoutes(keyAuthPlugin *unstructured.Unstructured, consumerName string, routeNames []string) error {
	spec, exists, _ := unstructured.NestedMap(keyAuthPlugin.Object, "spec")
	if !exists {
		return errors.New("WasmPlugin spec field not found")
	}

	// 获取现有的matchRules
	existingMatchRules, exists, _ := unstructured.NestedSlice(spec, "matchRules")
	if !exists {
		existingMatchRules = []interface{}{}
	}

	// 创建路由ID到规则的映射
	routeRulesMap := make(map[string]map[string]interface{})
	// 用于跟踪更新过的路由
	updatedRoutes := make(map[string]bool)

	// 首先收集现有规则并按路由ID组织
	for _, ruleItem := range existingMatchRules {
		rule, ok := ruleItem.(map[string]interface{})
		if !ok {
			continue
		}

		ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
		if !exists || len(ingressList) == 0 {
			continue
		}

		// 假设每个规则只有一个路由
		routeName, ok := ingressList[0].(string)
		if !ok {
			continue
		}

		// 保存路由到规则的映射
		routeRulesMap[routeName] = rule
	}

	// 从所有现有规则中移除该消费者
	for routeName, rule := range routeRulesMap {
		configMap, exists, _ := unstructured.NestedMap(rule, "config")
		if !exists {
			continue
		}

		allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
		if !exists {
			continue
		}

		// 过滤移除当前消费者
		newAllowList := []interface{}{}
		for _, allowItem := range allowList {
			if allowName, ok := allowItem.(string); ok && allowName != consumerName {
				newAllowList = append(newAllowList, allowName)
			}
		}

		// 更新允许列表
		_ = unstructured.SetNestedSlice(configMap, newAllowList, "allow")
		_ = unstructured.SetNestedMap(rule, configMap, "config")
		routeRulesMap[routeName] = rule
	}

	// 处理要添加的路由权限
	for _, routeName := range routeNames {
		updatedRoutes[routeName] = true

		if rule, exists := routeRulesMap[routeName]; exists {
			// 更新现有规则
			configMap, exists, _ := unstructured.NestedMap(rule, "config")
			if !exists {
				configMap = map[string]interface{}{
					"allow": []interface{}{consumerName},
				}
			} else {
				allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
				if !exists {
					allowList = []interface{}{}
				}

				// 确保消费者在列表中
				consumerExists := false
				for _, allowItem := range allowList {
					if name, ok := allowItem.(string); ok && name == consumerName {
						consumerExists = true
						break
					}
				}

				if !consumerExists {
					allowList = append(allowList, consumerName)
					_ = unstructured.SetNestedSlice(configMap, allowList, "allow")
				}
			}

			_ = unstructured.SetNestedMap(rule, configMap, "config")
			routeRulesMap[routeName] = rule
		} else {
			// 创建新规则
			routeRulesMap[routeName] = map[string]interface{}{
				"config": map[string]interface{}{
					"allow": []interface{}{consumerName},
				},
				"configDisable": false,
				"ingress":       []interface{}{routeName},
			}
		}
	}

	// 构建新的匹配规则列表
	newMatchRules := []interface{}{}

	// 添加所有规则，确保空的allow列表有虚拟消费者
	for _, rule := range routeRulesMap {
		configMap, exists, _ := unstructured.NestedMap(rule, "config")
		if exists {
			allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
			if exists {
				// 如果allow列表为空，添加虚拟消费者
				allowList = core.addVirtualConsumerToEmptyAllowArray(allowList)
				_ = unstructured.SetNestedSlice(configMap, allowList, "allow")
				_ = unstructured.SetNestedMap(rule, configMap, "config")
			}
		}
		newMatchRules = append(newMatchRules, rule)
	}

	// 更新匹配规则
	return unstructured.SetNestedSlice(keyAuthPlugin.Object, newMatchRules, "spec", "matchRules")
}

// DeleteConsumer 删除消费者
func (core *APIServerCore) DeleteConsumer(ctx csmContext.CsmContext) error {
	// 获取实例ID和消费者ID
	instanceId := ctx.Param("InstanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("InstanceId")
	}

	consumerId := ctx.Param("ConsumerID")
	if consumerId == "" {
		return csmErr.NewMissingParametersException("ConsumerID")
	}

	region := ctx.Get(reg.ContextRegion).(string)
	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}
	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("failed to create cluster client: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)
	ctx.CsmLogger().Infof("deleting consumer for instance %s, consumer %s from namespace %s", instanceId, consumerId, namespace)

	// 使用dynamic client获取WasmPlugin资源
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := hostingClient.Dynamic()
	unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to list WasmPlugins: %v", err)
		return err
	}

	// 从所有WasmPlugin中查找key-auth插件，然后查找指定的消费者
	var keyAuthPlugin *unstructured.Unstructured
	var foundConsumer bool
	var consumerName string

	for _, item := range unstructuredList.Items {
		// 检查是否为key-auth插件
		name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
		if name != constants.GetKeyAuthPluginName() {
			continue
		}

		keyAuthPlugin = &item

		// 获取消费者列表
		spec, exists, _ := unstructured.NestedMap(item.Object, "spec")
		if !exists {
			continue
		}

		defaultConfig, exists, _ := unstructured.NestedMap(spec, "defaultConfig")
		if !exists {
			continue
		}

		consumersData, exists, _ := unstructured.NestedSlice(defaultConfig, "consumers")
		if !exists {
			continue
		}

		// 遍历消费者列表，找到并删除指定ID的消费者
		updatedConsumers := []interface{}{}
		for _, consumerItem := range consumersData {
			consumer, ok := consumerItem.(map[string]interface{})
			if !ok {
				continue
			}

			// 提取消费者信息
			id, _, _ := unstructured.NestedString(consumer, "id")
			if id == consumerId {
				// 找到了要删除的消费者
				foundConsumer = true
				consumerName, _, _ = unstructured.NestedString(consumer, "name")
				// 不添加到更新后的列表中，即删除
				continue
			}

			// 保留其他消费者
			updatedConsumers = append(updatedConsumers, consumer)
		}

		// 如果找到了消费者，更新消费者列表
		if foundConsumer {
			// 新增：删除Redis中的配额记录
			ctx.CsmLogger().Infof("开始删除消费者 %s 的配额信息", consumerName)
			err := core.deleteConsumerQuota(ctx, instanceId, consumerName)
			if err != nil {
				// 配额删除失败不影响消费者删除，但需要记录错误日志
				ctx.CsmLogger().Errorf("删除消费者配额失败，但消费者删除成功: %v", err)
			} else {
				ctx.CsmLogger().Infof("成功删除消费者 %s 的配额信息", consumerName)
			}

			// 更新消费者列表
			err = unstructured.SetNestedSlice(keyAuthPlugin.Object, updatedConsumers, "spec", "defaultConfig", "consumers")
			if err != nil {
				return err
			}

			// 删除与该消费者相关的路由规则
			matchRules, exists, _ := unstructured.NestedSlice(spec, "matchRules")
			if exists {
				// 创建路由ID到规则的映射
				routeRulesMap := make(map[string]map[string]interface{})

				// 首先收集所有规则
				for _, ruleItem := range matchRules {
					rule, ok := ruleItem.(map[string]interface{})
					if !ok {
						continue
					}

					ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
					if !exists || len(ingressList) == 0 {
						continue
					}

					// 假设每个规则只对应一个路由
					routeName, ok := ingressList[0].(string)
					if !ok {
						continue
					}

					// 保存到映射
					routeRulesMap[routeName] = rule
				}

				// 从所有规则的allow列表中移除该消费者
				for routeName, rule := range routeRulesMap {
					configMap, exists, _ := unstructured.NestedMap(rule, "config")
					if !exists {
						continue
					}

					allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
					if !exists {
						continue
					}

					// 创建不包含该消费者的新列表
					newAllowList := []interface{}{}
					for _, allowItem := range allowList {
						if consumerAllowName, ok := allowItem.(string); ok && consumerAllowName != consumerName {
							newAllowList = append(newAllowList, consumerAllowName)
						}
					}

					// 更新allow列表
					_ = unstructured.SetNestedSlice(configMap, newAllowList, "allow")
					_ = unstructured.SetNestedMap(rule, configMap, "config")
					routeRulesMap[routeName] = rule
				}

				// 构建新的匹配规则列表，确保空的allow列表有虚拟消费者
				updatedMatchRules := []interface{}{}
				for _, rule := range routeRulesMap {
					configMap, exists, _ := unstructured.NestedMap(rule, "config")
					if exists {
						allowList, exists, _ := unstructured.NestedSlice(configMap, "allow")
						if exists {
							// 如果allow列表为空，添加虚拟消费者
							allowList = core.addVirtualConsumerToEmptyAllowArray(allowList)
							_ = unstructured.SetNestedSlice(configMap, allowList, "allow")
							_ = unstructured.SetNestedMap(rule, configMap, "config")
						}
					}
					updatedMatchRules = append(updatedMatchRules, rule)
				}

				// 更新匹配规则
				err := unstructured.SetNestedSlice(keyAuthPlugin.Object, updatedMatchRules, "spec", "matchRules")
				if err != nil {
					return err
				}
			}

			break
		}
	}

	// 如果没有找到消费者，则返回404错误
	if !foundConsumer {
		return csmErr.NewResourceNotFoundException(fmt.Sprintf("Consumer %s not found", consumerId))
	}

	// 更新key-auth插件
	_, err = dynamicClient.Resource(groupVersionResource).Namespace(namespace).Update(context.TODO(), keyAuthPlugin, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("failed to update key-auth plugin: %v", err)
		return err
	}

	// 返回成功响应
	response := meta.DeleteConsumerResponse{
		Success: true,
		Result:  nil,
		Status:  http.StatusOK,
	}

	return ctx.JSON(http.StatusOK, response)
}

// 将负载均衡算法转换为Istio支持的枚举值
func getLoadBalancerSimpleType(algorithm string) string {
	switch strings.ToLower(algorithm) {
	case "round-robin":
		return "ROUND_ROBIN"
	case "least-conn":
		return "LEAST_CONN"
	case "random":
		return "RANDOM"
	default:
		// 默认使用轮询
		return "ROUND_ROBIN"
	}
}

// createTokenRateLimitPlugin 创建或更新Token限流插件
// 如果集群中没有ai-token-limit插件，则安装限流插件
// 如果集群中有ai-token-limit插件，则只需要新增一条config
func (core *APIServerCore) createTokenRateLimitPlugin(
	ctx csmContext.CsmContext,
	gatewayNamespace string,
	routeName string,
	tokenRateLimit *meta.TokenRateLimit,
	client kube.Client) error {

	ctx.CsmLogger().Infof("开始处理Token限流插件，路由名称: %s", routeName)

	if tokenRateLimit == nil || !tokenRateLimit.Enabled {
		ctx.CsmLogger().Infof("Token限流未启用，跳过插件处理，路由: %s", routeName)
		return nil
	}

	if len(tokenRateLimit.RuleItems) == 0 {
		return errors.New("token rate limit is enabled but no rule items provided")
	}

	// 检查集群中是否存在ai-token-limit插件
	dynamicClient := client.Dynamic()
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	ctx.CsmLogger().Infof("检查集群中是否存在%s插件", constants.GetAITokenLimitPluginName())

	existingPlugin, err := dynamicClient.Resource(groupVersionResource).
		Namespace(gatewayNamespace).
		Get(context.TODO(), constants.GetAITokenLimitPluginName(), metav1.GetOptions{})

	if err != nil && !k8serrors.IsNotFound(err) {
		ctx.CsmLogger().Errorf("检查%s插件时发生错误: %v", constants.GetAITokenLimitPluginName(), err)
		return errors.Wrapf(err, "failed to check existing %s plugin", constants.GetAITokenLimitPluginName())
	}

	if k8serrors.IsNotFound(err) {
		// 插件不存在，需要安装新插件
		ctx.CsmLogger().Infof("集群中不存在%s插件，开始安装新插件", constants.GetAITokenLimitPluginName())
		return core.installTokenRateLimitPlugin(ctx, gatewayNamespace, routeName, tokenRateLimit, client)
	} else {
		// 插件已存在，添加新的配置规则
		ctx.CsmLogger().Infof("集群中已存在%s插件，添加新的配置规则", constants.GetAITokenLimitPluginName())
		return core.addTokenRateLimitConfig(ctx, existingPlugin, gatewayNamespace, routeName, tokenRateLimit, dynamicClient, groupVersionResource)
	}
}

// installTokenRateLimitPlugin 安装新的Token限流插件
func (core *APIServerCore) installTokenRateLimitPlugin(
	ctx csmContext.CsmContext,
	gatewayNamespace string,
	routeName string,
	tokenRateLimit *meta.TokenRateLimit,
	client kube.Client) error {

	ctx.CsmLogger().Infof("开始安装%s插件", constants.GetAITokenLimitPluginName())

	// 生成规则名称
	ruleName := fmt.Sprintf("token-limit-%s", strings.ToLower(routeName))

	// 准备模板数据
	templateData := meta.TokenRateLimitTemplateData{
		PluginName:       constants.GetAITokenLimitPluginName(),
		Namespace:        gatewayNamespace,
		RuleName:         ruleName,
		RouteName:        routeName,
		RuleItems:        tokenRateLimit.RuleItems,
		PluginURL:        constants.GetAITokenLimitPluginURL(),
		RedisServiceName: constants.GetRedisServiceName(),
		RedisServicePort: constants.GetRedisServicePort(),
		RedisPassword:    constants.GetRedisPassword(),
	}

	// 读取模板文件
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("获取当前目录失败: %v", err)
		return errors.Wrap(err, "failed to get current directory")
	}

	// 使用常量定义的模板路径
	templatePath := path.Join(pwd, constants.GetTokenRateLimitTemplatePath())
	templateBytes, err := os.ReadFile(templatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("读取Token限流模板文件失败: %v", err)
		return errors.Wrap(err, "failed to read token rate limit template file")
	}

	// 渲染模板
	tmpl, err := template.New("token-rate-limit").Parse(string(templateBytes))
	if err != nil {
		ctx.CsmLogger().Errorf("解析模板失败: %v", err)
		return errors.Wrap(err, "failed to parse template")
	}

	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, templateData); err != nil {
		ctx.CsmLogger().Errorf("渲染模板失败: %v", err)
		return errors.Wrap(err, "failed to render template")
	}

	// 应用渲染后的YAML
	objects, err := object.ManifestK8sObject(ctx, rendered.String())
	if err != nil {
		ctx.CsmLogger().Errorf("解析Token限流模板manifest失败: %v", err)
		return errors.Wrap(err, "failed to parse token rate limit template manifest")
	}

	// 使用通用的资源创建函数创建资源
	err = kube.CreateResources(ctx, client, objects)
	if err != nil {
		ctx.CsmLogger().Errorf("创建Token限流资源失败: %v", err)
		return errors.Wrap(err, "failed to create token rate limit resources")
	}

	ctx.CsmLogger().Infof("%s插件安装成功", constants.GetAITokenLimitPluginName())
	return nil
}

// addTokenRateLimitConfig 向现有的ai-token-limit插件添加新的配置规则
func (core *APIServerCore) addTokenRateLimitConfig(
	ctx csmContext.CsmContext,
	existingPlugin *unstructured.Unstructured,
	gatewayNamespace string,
	routeName string,
	tokenRateLimit *meta.TokenRateLimit,
	dynamicClient dynamic.Interface,
	groupVersionResource schema.GroupVersionResource) error {

	ctx.CsmLogger().Infof("向现有%s插件添加新配置，路由: %s", constants.GetAITokenLimitPluginName(), routeName)

	// 生成规则名称
	ruleName := fmt.Sprintf("token-limit-%s", strings.ToLower(routeName))

	// 获取现有的spec
	spec, exists, err := unstructured.NestedMap(existingPlugin.Object, "spec")
	if !exists || err != nil {
		ctx.CsmLogger().Errorf("获取插件spec失败: %v", err)
		return errors.New("failed to get plugin spec")
	}

	// 获取现有的matchRules
	matchRules, exists, err := unstructured.NestedSlice(spec, "matchRules")
	if !exists || err != nil {
		ctx.CsmLogger().Infof("插件中不存在matchRules，创建新的规则列表")
		matchRules = []interface{}{}
	}

	// 检查是否已存在相同路由的规则，如果存在则先移除
	var updatedMatchRules []interface{}
	for _, ruleItem := range matchRules {
		rule, ok := ruleItem.(map[string]interface{})
		if !ok {
			continue
		}

		ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
		if !exists {
			updatedMatchRules = append(updatedMatchRules, rule)
			continue
		}

		// 检查是否包含当前路由
		containsCurrentRoute := false
		for _, ing := range ingressList {
			if routeStr, ok := ing.(string); ok && routeStr == routeName {
				containsCurrentRoute = true
				break
			}
		}

		// 如果不包含当前路由，保留该规则
		if !containsCurrentRoute {
			updatedMatchRules = append(updatedMatchRules, rule)
		} else {
			ctx.CsmLogger().Infof("移除路由%s的旧限流规则", routeName)
		}
	}

	// 创建新的配置规则
	newConfig := map[string]interface{}{
		"redis": map[string]interface{}{
			"password":     constants.GetRedisPassword(),
			"service_name": constants.GetRedisServiceName(),
			"service_port": int64(constants.GetRedisServicePort()),
		},
		"rule_name":  ruleName,
		"rule_items": core.buildRuleItemsForPlugin(tokenRateLimit.RuleItems),
	}

	// 创建新的匹配规则
	newMatchRule := map[string]interface{}{
		"config":        newConfig,
		"configDisable": false,
		"ingress":       []interface{}{routeName},
	}

	// 添加新规则到列表
	updatedMatchRules = append(updatedMatchRules, newMatchRule)

	// 更新插件的matchRules
	if err := unstructured.SetNestedSlice(existingPlugin.Object, updatedMatchRules, "spec", "matchRules"); err != nil {
		ctx.CsmLogger().Errorf("更新插件matchRules失败: %v", err)
		return errors.Wrap(err, "failed to update plugin matchRules")
	}

	// 更新插件
	_, err = dynamicClient.Resource(groupVersionResource).
		Namespace(gatewayNamespace).
		Update(context.TODO(), existingPlugin, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("更新%s插件失败: %v", constants.GetAITokenLimitPluginName(), err)
		return errors.Wrapf(err, "failed to update %s plugin", constants.GetAITokenLimitPluginName())
	}

	ctx.CsmLogger().Infof("成功向%s插件添加路由%s的限流配置", constants.GetAITokenLimitPluginName(), routeName)
	return nil
}

// buildRuleItemsForPlugin 构建插件配置中的rule_items
func (core *APIServerCore) buildRuleItemsForPlugin(ruleItems []meta.TokenRateLimitRuleItem) []interface{} {
	var pluginRuleItems []interface{}

	for _, item := range ruleItems {
		pluginRuleItem := make(map[string]interface{})

		// 根据匹配条件类型设置限流字段
		switch item.MatchCondition.Type {
		case "consumer":
			pluginRuleItem["limit_by_consumer"] = ""
		case "header":
			pluginRuleItem["limit_by_header"] = item.MatchCondition.Key
		case "query_param":
			pluginRuleItem["limit_by_param"] = item.MatchCondition.Key
		}

		// 构建limit_keys
		limitKey := map[string]interface{}{
			"key": item.MatchCondition.Value,
		}

		// 根据时间单位设置对应的token数量
		switch item.LimitConfig.TimeUnit {
		case "second":
			limitKey["token_per_second"] = int64(item.LimitConfig.TokenAmount)
		case "minute":
			limitKey["token_per_minute"] = int64(item.LimitConfig.TokenAmount)
		case "hour":
			limitKey["token_per_hour"] = int64(item.LimitConfig.TokenAmount)
		case "day":
			limitKey["token_per_day"] = int64(item.LimitConfig.TokenAmount)
		default:
			// 默认使用分钟
			limitKey["token_per_minute"] = int64(item.LimitConfig.TokenAmount)
		}

		pluginRuleItem["limit_keys"] = []interface{}{limitKey}
		pluginRuleItems = append(pluginRuleItems, pluginRuleItem)
	}

	return pluginRuleItems
}

// extractTokenRateLimitInfoForRoute 从 WasmPlugin 对象中提取指定路由的限流规则信息
func extractTokenRateLimitInfoForRoute(plugin *unstructured.Unstructured, routeName string) ([]meta.TokenRateLimitRuleItem, bool, string) {
	var ruleItems []meta.TokenRateLimitRuleItem
	isEnabled := false
	ruleName := ""

	// 从 spec 中提取信息
	spec, exists, _ := unstructured.NestedMap(plugin.Object, "spec")
	if !exists {
		return ruleItems, false, ""
	}

	// 获取匹配规则
	matchRules, exists, _ := unstructured.NestedSlice(spec, "matchRules")
	if !exists || len(matchRules) == 0 {
		return ruleItems, false, ""
	}

	// 查找与当前路由相关的规则
	for _, matchRuleObj := range matchRules {
		matchRule, ok := matchRuleObj.(map[string]interface{})
		if !ok {
			continue
		}

		// 检查是否包含当前路由
		if !containsRoute(matchRule, routeName) {
			continue
		}

		// 提取规则信息
		isEnabled, ruleName = extractRuleStatus(matchRule)
		ruleItems = extractRuleItems(matchRule)
		break // 找到了当前路由的规则，跳出循环
	}

	return ruleItems, isEnabled, ruleName
}

// containsRoute 检查匹配规则是否包含指定路由
func containsRoute(matchRule map[string]interface{}, routeName string) bool {
	ingressList, exists, _ := unstructured.NestedSlice(matchRule, "ingress")
	if !exists {
		return false
	}

	for _, ing := range ingressList {
		if routeStr, ok := ing.(string); ok && routeStr == routeName {
			return true
		}
	}
	return false
}

// extractRuleStatus 提取规则状态和名称
func extractRuleStatus(matchRule map[string]interface{}) (bool, string) {
	isEnabled := true
	ruleName := ""

	// 检查是否启用
	configDisable, exists, _ := unstructured.NestedBool(matchRule, "configDisable")
	if exists && configDisable {
		isEnabled = false
	}

	// 获取配置
	config, exists, _ := unstructured.NestedMap(matchRule, "config")
	if exists {
		if ruleNameStr, exists, _ := unstructured.NestedString(config, "rule_name"); exists {
			ruleName = ruleNameStr
		}
	}

	return isEnabled, ruleName
}

// extractRuleItems 提取规则项列表
func extractRuleItems(matchRule map[string]interface{}) []meta.TokenRateLimitRuleItem {
	var ruleItems []meta.TokenRateLimitRuleItem

	// 获取配置
	config, exists, _ := unstructured.NestedMap(matchRule, "config")
	if !exists {
		return ruleItems
	}

	// 获取规则项
	ruleItemsObj, exists, _ := unstructured.NestedSlice(config, "rule_items")
	if !exists || len(ruleItemsObj) == 0 {
		return ruleItems
	}

	// 遍历规则项
	for _, ruleItemObj := range ruleItemsObj {
		ruleItem, ok := ruleItemObj.(map[string]interface{})
		if !ok {
			continue
		}

		// 提取单个规则项
		if item := extractSingleRuleItem(ruleItem); item != nil {
			ruleItems = append(ruleItems, *item)
		}
	}

	return ruleItems
}

// extractSingleRuleItem 提取单个规则项
func extractSingleRuleItem(ruleItem map[string]interface{}) *meta.TokenRateLimitRuleItem {
	// 构建匹配条件
	matchCondition := extractMatchCondition(ruleItem)
	if matchCondition == nil {
		return nil
	}

	// 获取limit_keys
	limitKeysObj, exists, _ := unstructured.NestedSlice(ruleItem, "limit_keys")
	if !exists || len(limitKeysObj) == 0 {
		return nil
	}

	// 获取第一个限流键的信息
	limitKey, ok := limitKeysObj[0].(map[string]interface{})
	if !ok {
		return nil
	}

	// 提取限流配置
	limitConfig := extractLimitConfig(limitKey, matchCondition)
	if limitConfig == nil {
		return nil
	}

	return &meta.TokenRateLimitRuleItem{
		MatchCondition: *matchCondition,
		LimitConfig:    *limitConfig,
	}
}

// extractMatchCondition 提取匹配条件
func extractMatchCondition(ruleItem map[string]interface{}) *meta.TokenRateLimitMatchCondition {
	var matchCondition meta.TokenRateLimitMatchCondition

	// 检查限流类型
	if _, exists, _ := unstructured.NestedString(ruleItem, "limit_by_consumer"); exists {
		matchCondition.Type = "consumer"
		matchCondition.Key = ""
		return &matchCondition
	}

	if limitByHeader, exists, _ := unstructured.NestedString(ruleItem, "limit_by_header"); exists && limitByHeader != "" {
		matchCondition.Type = "header"
		matchCondition.Key = fmt.Sprintf("%v", limitByHeader)
		return &matchCondition
	}

	if limitByParam, exists, _ := unstructured.NestedString(ruleItem, "limit_by_param"); exists && limitByParam != "" {
		matchCondition.Type = "query_param"
		matchCondition.Key = fmt.Sprintf("%v", limitByParam)
		return &matchCondition
	}

	return nil
}

// extractLimitConfig 提取限流配置
func extractLimitConfig(limitKey map[string]interface{}, matchCondition *meta.TokenRateLimitMatchCondition) *meta.TokenRateLimitConfig {
	var limitConfig meta.TokenRateLimitConfig

	// 获取key和限流配置（支持多种类型）
	if keyValueRaw, exists, _ := unstructured.NestedFieldNoCopy(limitKey, "key"); exists && keyValueRaw != nil {
		matchCondition.Value = fmt.Sprintf("%v", keyValueRaw)
	}

	// 获取限流配置
	if tokenPerSecond, exists, _ := unstructured.NestedInt64(limitKey, "token_per_second"); exists {
		limitConfig.TimeUnit = "second"
		limitConfig.TokenAmount = int(tokenPerSecond)
		return &limitConfig
	}

	if tokenPerMinute, exists, _ := unstructured.NestedInt64(limitKey, "token_per_minute"); exists {
		limitConfig.TimeUnit = "minute"
		limitConfig.TokenAmount = int(tokenPerMinute)
		return &limitConfig
	}

	if tokenPerHour, exists, _ := unstructured.NestedInt64(limitKey, "token_per_hour"); exists {
		limitConfig.TimeUnit = "hour"
		limitConfig.TokenAmount = int(tokenPerHour)
		return &limitConfig
	}

	if tokenPerDay, exists, _ := unstructured.NestedInt64(limitKey, "token_per_day"); exists {
		limitConfig.TimeUnit = "day"
		limitConfig.TokenAmount = int(tokenPerDay)
		return &limitConfig
	}

	return nil
}

// deleteTokenRateLimitRule 从ai-token-limit插件中删除指定路由的限流规则
func (core *APIServerCore) deleteTokenRateLimitRule(
	ctx csmContext.CsmContext,
	gatewayNamespace string,
	routeName string,
	client kube.Client) error {

	ctx.CsmLogger().Infof("开始从%s插件中删除路由%s的限流规则", constants.GetAITokenLimitPluginName(), routeName)

	// 获取动态客户端
	dynamicClient := client.Dynamic()
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	// 查找ai-token-limit插件
	tokenLimitPlugin, err := dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).Get(
		context.TODO(), constants.GetAITokenLimitPluginName(), metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			ctx.CsmLogger().Infof("%s插件不存在，无需删除限流规则", constants.GetAITokenLimitPluginName())
			return nil
		}
		ctx.CsmLogger().Errorf("获取%s插件时发生错误: %v", constants.GetAITokenLimitPluginName(), err)
		return nil // 不影响主流程，只记录错误
	}

	// 获取插件的spec配置
	spec, exists, err := unstructured.NestedMap(tokenLimitPlugin.Object, "spec")
	if !exists || err != nil {
		ctx.CsmLogger().Errorf("获取%s插件spec配置失败: %v", constants.GetAITokenLimitPluginName(), err)
		return nil // 不影响主流程
	}

	// 获取matchRules
	matchRules, exists, err := unstructured.NestedSlice(spec, "matchRules")
	if !exists || err != nil {
		ctx.CsmLogger().Infof("%s插件中没有matchRules配置", constants.GetAITokenLimitPluginName())
		return nil
	}

	// 过滤掉与当前路由相关的规则
	updatedMatchRules := []interface{}{}
	ruleRemoved := false

	for _, ruleItem := range matchRules {
		rule, ok := ruleItem.(map[string]interface{})
		if !ok {
			continue
		}

		// 获取ingress列表
		ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
		if !exists {
			// 保留没有ingress字段的规则
			updatedMatchRules = append(updatedMatchRules, rule)
			continue
		}

		// 检查ingress列表中是否包含要删除的路由
		keepRule := true
		for _, ingressItem := range ingressList {
			if routeValue, ok := ingressItem.(string); ok && routeValue == routeName {
				keepRule = false
				ruleRemoved = true
				break
			}
		}

		if keepRule {
			updatedMatchRules = append(updatedMatchRules, rule)
		}
	}

	// 如果没有找到相关规则，直接返回
	if !ruleRemoved {
		ctx.CsmLogger().Infof("在%s插件中未找到路由%s的限流规则", constants.GetAITokenLimitPluginName(), routeName)
		return nil
	}

	// 更新matchRules
	err = unstructured.SetNestedSlice(tokenLimitPlugin.Object, updatedMatchRules, "spec", "matchRules")
	if err != nil {
		ctx.CsmLogger().Errorf("更新%s插件matchRules失败: %v", constants.GetAITokenLimitPluginName(), err)
		return nil // 不影响主流程
	}

	// 保存更新后的插件配置
	_, err = dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).Update(
		context.TODO(), tokenLimitPlugin, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("保存%s插件配置失败: %v", constants.GetAITokenLimitPluginName(), err)
		return nil // 不影响主流程
	}

	ctx.CsmLogger().Infof("成功从%s插件中删除路由%s的限流规则", constants.GetAITokenLimitPluginName(), routeName)
	return nil
}

// createBaseAIStatisticsPlugin 在创建网关时创建基础的AI Statistics插件
func (core *APIServerCore) createBaseAIStatisticsPlugin(
	ctx csmContext.CsmContext,
	gatewayNamespace string,
	client kube.Client) error {

	ctx.CsmLogger().Infof("开始为网关%s创建基础AI Statistics插件", gatewayNamespace)

	// 准备基础插件数据
	basePluginData := meta.AIStatisticsTemplateData{
		Namespace:  gatewayNamespace,
		PluginName: constants.GetAIStatisticsPluginName(),
		PluginURL:  constants.GetAIStatisticsPluginURL(),
	}

	// 读取模板文件
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("获取当前目录失败: %v", err)
		return errors.Wrap(err, "failed to get current directory")
	}

	// 使用AI Statistics模板路径
	aiStatisticsTemplatePath := path.Join(pwd, constants.GetAIStatisticsTemplatePath())
	ctx.CsmLogger().Infof("尝试读取AI Statistics模板文件: %s", aiStatisticsTemplatePath)

	templateBytes, err := os.ReadFile(aiStatisticsTemplatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("读取AI Statistics模板文件失败: %v", err)
		return errors.Wrap(err, "failed to read AI Statistics template file")
	}

	// 渲染模板
	tmpl, err := template.New("base-ai-statistics").Parse(string(templateBytes))
	if err != nil {
		ctx.CsmLogger().Errorf("解析AI Statistics模板失败: %v", err)
		return errors.Wrap(err, "failed to parse AI Statistics template")
	}

	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, basePluginData); err != nil {
		ctx.CsmLogger().Errorf("渲染AI Statistics模板失败: %v", err)
		return errors.Wrap(err, "failed to render AI Statistics template")
	}

	// 应用渲染后的YAML
	aiStatisticsObjects, err := object.ManifestK8sObject(ctx, rendered.String())
	if err != nil {
		ctx.CsmLogger().Errorf("解析AI Statistics模板manifest失败: %v", err)
		return errors.Wrap(err, "failed to parse AI Statistics template manifest")
	}

	// 创建资源
	err = kube.CreateResources(ctx, client, aiStatisticsObjects)
	if err != nil {
		ctx.CsmLogger().Errorf("创建AI Statistics插件资源失败: %v", err)
		return errors.Wrap(err, "failed to create AI Statistics plugin resources")
	}

	ctx.CsmLogger().Infof("成功为网关%s创建基础AI Statistics插件", gatewayNamespace)
	return nil
}

// createBaseKeyAuthPlugin 在创建网关时创建基础的Key Auth插件
func (core *APIServerCore) createBaseKeyAuthPlugin(
	ctx csmContext.CsmContext,
	gatewayNamespace string,
	client kube.Client) error {

	ctx.CsmLogger().Infof("开始为网关%s创建基础Key Auth插件", gatewayNamespace)

	// 准备基础插件数据，包含虚拟消费者
	virtualConsumerName := constants.GetVirtualDenyAllConsumerName()
	basePluginData := meta.ConsumerTemplateData{
		Namespace:      gatewayNamespace,
		ConsumerID:     "virtual-deny-all-id",
		ConsumerName:   virtualConsumerName,
		Description:    "系统虚拟消费者，用于处理空allow数组",
		Credential:     "virtual-token-deny-all",
		AuthType:       "KeyAuth",
		RouteNames:     []string{}, // 空的路由列表
		CreateTime:     time.Now().In(time.FixedZone("CST", 8*60*60)).Format("2006-01-02 15:04:05"),
		UnlimitedQuota: true, // 虚拟消费者使用无限配额，避免totalQuota字段问题
		TotalQuota:     nil,  // 无限配额时不设置totalQuota
		// 插件相关字段
		PluginName: constants.GetKeyAuthPluginName(),
		PluginURL:  constants.GetKeyAuthPluginURL(),
	}

	// 读取模板文件
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("获取当前目录失败: %v", err)
		return errors.Wrap(err, "failed to get current directory")
	}

	// 使用Key Auth模板路径
	keyAuthTemplatePath := path.Join(pwd, constants.GetKeyAuthTemplatePath())
	ctx.CsmLogger().Infof("尝试读取Key Auth模板文件: %s", keyAuthTemplatePath)

	templateBytes, err := os.ReadFile(keyAuthTemplatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("读取Key Auth模板文件失败: %v", err)
		return errors.Wrap(err, "failed to read Key Auth template file")
	}

	// 渲染模板
	tmpl, err := template.New("base-key-auth").Parse(string(templateBytes))
	if err != nil {
		ctx.CsmLogger().Errorf("解析Key Auth模板失败: %v", err)
		return errors.Wrap(err, "failed to parse Key Auth template")
	}

	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, basePluginData); err != nil {
		ctx.CsmLogger().Errorf("渲染Key Auth模板失败: %v", err)
		return errors.Wrap(err, "failed to render Key Auth template")
	}

	// 应用渲染后的YAML
	keyAuthObjects, err := object.ManifestK8sObject(ctx, rendered.String())
	if err != nil {
		ctx.CsmLogger().Errorf("解析Key Auth模板manifest失败: %v", err)
		return errors.Wrap(err, "failed to parse Key Auth template manifest")
	}

	// 创建资源
	err = kube.CreateResources(ctx, client, keyAuthObjects)
	if err != nil {
		ctx.CsmLogger().Errorf("创建Key Auth插件资源失败: %v", err)
		return errors.Wrap(err, "failed to create Key Auth plugin resources")
	}

	ctx.CsmLogger().Infof("成功为网关%s创建基础Key Auth插件", gatewayNamespace)
	return nil
}

// createBaseAIQuotaPlugin 在创建网关时创建基础的AI配额插件
func (core *APIServerCore) createBaseAIQuotaPlugin(
	ctx csmContext.CsmContext,
	gatewayNamespace string,
	client kube.Client) error {

	ctx.CsmLogger().Infof("开始为网关%s创建基础AI配额插件", gatewayNamespace)

	// 准备基础插件数据，包含虚拟路由
	basePluginData := meta.AIQuotaTemplateData{
		Namespace:        gatewayNamespace,
		RouteNames:       []string{constants.GetVirtualDenyAllConsumerName()}, // 使用虚拟路由名称
		RedisKeyPrefix:   fmt.Sprintf("chat_quota_%s:", strings.TrimPrefix(gatewayNamespace, "istio-system-")),
		PluginName:       constants.GetAIQuotaPluginName(),
		PluginURL:        constants.GetAIQuotaPluginURL(),
		RedisServiceName: constants.GetRedisServiceName(),
		RedisServicePort: constants.GetRedisServicePort(),
		RedisPassword:    constants.GetRedisPassword(),
	}

	// 读取模板文件
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("获取当前目录失败: %v", err)
		return errors.Wrap(err, "failed to get current directory")
	}

	// 使用AI配额模板路径
	aiQuotaTemplatePath := path.Join(pwd, constants.GetAIQuotaTemplatePath())
	ctx.CsmLogger().Infof("尝试读取AI配额模板文件: %s", aiQuotaTemplatePath)

	templateBytes, err := os.ReadFile(aiQuotaTemplatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("读取AI配额模板文件失败: %v", err)
		return errors.Wrap(err, "failed to read AI Quota template file")
	}

	// 渲染模板
	tmpl, err := template.New("base-ai-quota").Parse(string(templateBytes))
	if err != nil {
		ctx.CsmLogger().Errorf("解析AI配额模板失败: %v", err)
		return errors.Wrap(err, "failed to parse AI Quota template")
	}

	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, basePluginData); err != nil {
		ctx.CsmLogger().Errorf("渲染AI配额模板失败: %v", err)
		return errors.Wrap(err, "failed to render AI Quota template")
	}

	// 应用渲染后的YAML
	aiQuotaObjects, err := object.ManifestK8sObject(ctx, rendered.String())
	if err != nil {
		ctx.CsmLogger().Errorf("解析AI配额模板manifest失败: %v", err)
		return errors.Wrap(err, "failed to parse AI Quota template manifest")
	}

	// 创建资源
	err = kube.CreateResources(ctx, client, aiQuotaObjects)
	if err != nil {
		ctx.CsmLogger().Errorf("创建AI配额插件资源失败: %v", err)
		return errors.Wrap(err, "failed to create AI Quota plugin resources")
	}

	ctx.CsmLogger().Infof("成功为网关%s创建基础AI配额插件", gatewayNamespace)
	return nil
}

// updateAIQuotaPluginIngress 更新AI配额插件的ingress列表
func (core *APIServerCore) updateAIQuotaPluginIngress(
	ctx csmContext.CsmContext,
	gatewayNamespace string,
	routeName string,
	operation string, // "add" 或 "remove"
	client kube.Client) error {

	ctx.CsmLogger().Infof("开始%s AI配额插件的ingress列表，路由: %s", operation, routeName)

	// 获取动态客户端
	dynamicClient := client.Dynamic()
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	// 查找AI配额插件
	unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to list WasmPlugins: %v", err)
		return err
	}

	var aiQuotaPlugin *unstructured.Unstructured
	for _, item := range unstructuredList.Items {
		name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
		if name == constants.GetAIQuotaPluginName() {
			aiQuotaPlugin = &item
			break
		}
	}

	// 如果没有找到AI配额插件，创建基础插件
	if aiQuotaPlugin == nil {
		ctx.CsmLogger().Infof("AI配额插件不存在，正在创建基础插件")
		err := core.createBaseAIQuotaPlugin(ctx, gatewayNamespace, client)
		if err != nil {
			ctx.CsmLogger().Errorf("创建基础AI配额插件失败: %v", err)
			return err
		}

		// 重新查询插件
		unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})
		if err != nil {
			ctx.CsmLogger().Errorf("重新查询WasmPlugins失败: %v", err)
			return err
		}

		for _, item := range unstructuredList.Items {
			name, _, _ := unstructured.NestedString(item.Object, "metadata", "name")
			if name == constants.GetAIQuotaPluginName() {
				aiQuotaPlugin = &item
				break
			}
		}

		if aiQuotaPlugin == nil {
			return errors.New("创建AI配额插件后仍然无法找到插件")
		}
	}

	// 获取现有的matchRules
	spec, exists, _ := unstructured.NestedMap(aiQuotaPlugin.Object, "spec")
	if !exists {
		spec = map[string]interface{}{}
		aiQuotaPlugin.Object["spec"] = spec
	}

	matchRules, exists, _ := unstructured.NestedSlice(spec, "matchRules")
	if !exists {
		matchRules = []interface{}{}
	}

	// 处理ingress列表
	var updatedMatchRules []interface{}
	ruleFound := false

	for _, ruleItem := range matchRules {
		rule, ok := ruleItem.(map[string]interface{})
		if !ok {
			updatedMatchRules = append(updatedMatchRules, ruleItem)
			continue
		}

		ingressList, exists, _ := unstructured.NestedSlice(rule, "ingress")
		if !exists {
			updatedMatchRules = append(updatedMatchRules, rule)
			continue
		}

		// 更新ingress列表
		var newIngressList []interface{}
		for _, ingressItem := range ingressList {
			if routeStr, ok := ingressItem.(string); ok {
				if routeStr == routeName {
					ruleFound = true
					if operation == "add" {
						// 如果是添加操作且路由已存在，保留
						newIngressList = append(newIngressList, ingressItem)
					}
					// 如果是删除操作，不添加到新列表中
				} else {
					// 保留其他路由
					newIngressList = append(newIngressList, ingressItem)
				}
			}
		}

		// 如果是添加操作且路由不存在，添加路由
		if operation == "add" && !ruleFound {
			newIngressList = append(newIngressList, routeName)
			ruleFound = true
		}

		// 如果ingress列表为空，添加虚拟路由
		if len(newIngressList) == 0 {
			newIngressList = append(newIngressList, constants.GetVirtualDenyAllConsumerName())
		}

		// 更新规则的ingress列表
		rule["ingress"] = newIngressList
		updatedMatchRules = append(updatedMatchRules, rule)
	}

	// 如果是添加操作且没有找到任何规则，创建新规则
	if operation == "add" && !ruleFound {
		instanceId := strings.TrimPrefix(gatewayNamespace, "istio-system-")

		newRule := map[string]interface{}{
			"config": map[string]interface{}{
				"admin_consumer": "admin",
				"admin_path":     "/quota",
				"redis": map[string]interface{}{
					"service_name": constants.GetRedisServiceName(),
					"service_port": constants.GetRedisServicePort(),
					"password":     constants.GetRedisPassword(),
					"timeout":      2000,
				},
				"redis_key_prefix": fmt.Sprintf("chat_quota_%s:", instanceId),
			},
			"configDisable": false,
			"ingress":       []interface{}{routeName},
		}
		updatedMatchRules = append(updatedMatchRules, newRule)
	}

	// 更新matchRules
	err = unstructured.SetNestedSlice(aiQuotaPlugin.Object, updatedMatchRules, "spec", "matchRules")
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to update matchRules: %v", err)
		return err
	}

	// 更新AI配额插件
	_, err = dynamicClient.Resource(groupVersionResource).Namespace(gatewayNamespace).Update(
		context.TODO(), aiQuotaPlugin, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("Failed to update AI Quota plugin: %v", err)
		return err
	}

	ctx.CsmLogger().Infof("成功%s AI配额插件的ingress列表，路由: %s", operation, routeName)
	return nil
}

// setConsumerQuota 设置消费者配额到Redis
func (core *APIServerCore) setConsumerQuota(ctx csmContext.CsmContext, instanceID, consumerName string, unlimitedQuota bool, quotaValue *int) error {
	// 初始化Redis服务
	redisService, err := core.initRedisService(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("初始化Redis服务失败: %v", err)
		return errors.Wrap(err, "failed to initialize redis service")
	}
	defer func() {
		if disconnectErr := redisService.Disconnect(); disconnectErr != nil {
			ctx.CsmLogger().Errorf("断开Redis连接失败: %v", disconnectErr)
		}
	}()

	// 构造Redis key
	redisKey := fmt.Sprintf("chat_quota_%s:%s", instanceID, consumerName)

	// 根据配额类型设置值
	var quotaValueStr string
	if unlimitedQuota {
		// 无限配额设置为math.MaxInt64
		quotaValueStr = fmt.Sprintf("%d", math.MaxInt64)
		ctx.CsmLogger().Infof("设置消费者 %s 为无限配额", consumerName)
	} else {
		// 限制配额设置为具体数值
		if quotaValue == nil {
			return errors.New("当unlimitedQuota为false时，quotaValue不能为空")
		}
		quotaValueStr = fmt.Sprintf("%d", *quotaValue)
		ctx.CsmLogger().Infof("设置消费者 %s 配额为 %d", consumerName, *quotaValue)
	}

	// 设置配额值到Redis
	err = redisService.Set(ctx, redisKey, quotaValueStr)
	if err != nil {
		ctx.CsmLogger().Errorf("设置Redis配额值失败: %v", err)
		return errors.Wrap(err, "failed to set quota value in redis")
	}

	ctx.CsmLogger().Infof("成功设置消费者 %s 的配额值到Redis，key: %s, value: %s", consumerName, redisKey, quotaValueStr)
	return nil
}

// getConsumerQuota 从Redis获取消费者配额
func (core *APIServerCore) getConsumerQuota(ctx csmContext.CsmContext, instanceID, consumerName string) (int64, error) {
	// 初始化Redis服务
	redisService, err := core.initRedisService(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("初始化Redis服务失败: %v", err)
		return 0, errors.Wrap(err, "failed to initialize redis service")
	}
	defer func() {
		if disconnectErr := redisService.Disconnect(); disconnectErr != nil {
			ctx.CsmLogger().Errorf("断开Redis连接失败: %v", disconnectErr)
		}
	}()

	// 构造Redis key
	redisKey := fmt.Sprintf("chat_quota_%s:%s", instanceID, consumerName)

	// 从Redis获取配额值
	quotaValueStr, err := redisService.Get(ctx, redisKey)
	if err != nil {
		ctx.CsmLogger().Warnf("获取Redis配额值失败: %v", err)
		return 0, nil // 返回0作为默认值
	}

	// 转换为int64
	quotaValue, err := strconv.ParseInt(quotaValueStr, 10, 64)
	if err != nil {
		ctx.CsmLogger().Errorf("解析配额值失败: %v", err)
		return 0, nil // 返回0作为默认值
	}

	ctx.CsmLogger().Infof("成功获取消费者 %s 的配额值: %d", consumerName, quotaValue)
	return quotaValue, nil
}

// deleteConsumerQuota 从Redis删除消费者配额
func (core *APIServerCore) deleteConsumerQuota(ctx csmContext.CsmContext, instanceID, consumerName string) error {
	// 初始化Redis服务
	redisService, err := core.initRedisService(ctx)
	if err != nil {
		ctx.CsmLogger().Errorf("初始化Redis服务失败: %v", err)
		return errors.Wrap(err, "failed to initialize redis service")
	}
	defer func() {
		if disconnectErr := redisService.Disconnect(); disconnectErr != nil {
			ctx.CsmLogger().Errorf("断开Redis连接失败: %v", disconnectErr)
		}
	}()

	// 构造Redis key
	redisKey := fmt.Sprintf("chat_quota_%s:%s", instanceID, consumerName)

	// 从Redis删除配额值
	err = redisService.Delete(ctx, redisKey)
	if err != nil {
		ctx.CsmLogger().Errorf("删除Redis配额值失败: %v", err)
		return errors.Wrap(err, "failed to delete quota value from redis")
	}

	ctx.CsmLogger().Infof("成功删除消费者 %s 的配额值，key: %s", consumerName, redisKey)
	return nil
}

// initRedisService 初始化Redis服务
func (core *APIServerCore) initRedisService(ctx csmContext.CsmContext) (redisService.ServiceInterface, error) {
	// 从配置文件中读取Redis配置
	env := viper.GetString("aigw.redis.environment")
	if env == "" {
		env = "offline" // 默认使用offline环境
	}

	redisAddress := viper.GetString(fmt.Sprintf("aigw.redis.%s.address", env))
	redisPort := viper.GetInt(fmt.Sprintf("aigw.redis.%s.service_port", env))
	redisPassword := viper.GetString(fmt.Sprintf("aigw.redis.%s.password", env))

	if redisAddress == "" {
		return nil, errors.New("Redis地址配置为空")
	}

	ctx.CsmLogger().Infof("使用Redis配置 - 环境: %s, 地址: %s, 端口: %d", env, redisAddress, redisPort)

	// 创建Redis服务配置
	redisOption := redisService.NewOption(redisAddress, redisPort, redisPassword)

	// 创建Redis服务实例
	redisServiceInstance := redisService.NewRedisService(redisOption)

	// 连接Redis
	if err := redisServiceInstance.Connect(ctx); err != nil {
		ctx.CsmLogger().Errorf("连接Redis失败: %v", err)
		return nil, errors.Wrap(err, "failed to connect to redis")
	}

	ctx.CsmLogger().Infof("成功初始化Redis服务")
	return redisServiceInstance, nil
}

// ensureVirtualConsumerExists 确保虚拟消费者存在于consumers列表中
func (core *APIServerCore) ensureVirtualConsumerExists(consumersData []interface{}) []interface{} {
	virtualConsumerName := constants.GetVirtualDenyAllConsumerName()

	// 检查虚拟消费者是否已存在
	for _, consumerItem := range consumersData {
		consumer, ok := consumerItem.(map[string]interface{})
		if !ok {
			continue
		}
		name, _, _ := unstructured.NestedString(consumer, "name")
		if name == virtualConsumerName {
			return consumersData // 虚拟消费者已存在
		}
	}

	// 虚拟消费者不存在，创建并添加
	virtualConsumer := map[string]interface{}{
		"credential":     "virtual-token-deny-all",
		"name":           virtualConsumerName,
		"id":             "virtual-deny-all-id",
		"description":    "系统虚拟消费者，用于处理空allow数组",
		"authType":       "KeyAuth",
		"createTime":     time.Now().In(time.FixedZone("CST", 8*60*60)).Format("2006-01-02 15:04:05"),
		"unlimitedQuota": true, // 虚拟消费者应该使用无限配额，避免totalQuota字段问题
	}

	return append(consumersData, virtualConsumer)
}

// addVirtualConsumerToEmptyAllowArray 当allow数组为空时添加虚拟消费者
func (core *APIServerCore) addVirtualConsumerToEmptyAllowArray(allowList []interface{}) []interface{} {
	if len(allowList) == 0 {
		virtualConsumerName := constants.GetVirtualDenyAllConsumerName()
		return []interface{}{virtualConsumerName}
	}
	return allowList
}

// isVirtualConsumer 检查是否为虚拟消费者
func (core *APIServerCore) isVirtualConsumer(consumerName string) bool {
	return consumerName == constants.GetVirtualDenyAllConsumerName()
}

// filterVirtualConsumer 从消费者列表中过滤掉虚拟消费者
func (core *APIServerCore) filterVirtualConsumer(consumers []string) []string {
	virtualConsumerName := constants.GetVirtualDenyAllConsumerName()
	filtered := []string{}
	for _, consumer := range consumers {
		if consumer != virtualConsumerName {
			filtered = append(filtered, consumer)
		}
	}
	return filtered
}

// CreateIPRestriction 创建IP黑白名单
func (core *APIServerCore) CreateIPRestriction(ctx csmContext.CsmContext) error {
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取实例ID
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}
	ctx.CsmLogger().Infof("开始创建IP黑白名单，实例ID: %s", instanceId)

	// 绑定请求参数
	createIPRestrictionReq := &meta.CreateIPRestrictionRequest{}
	if err := ctx.Bind(createIPRestrictionReq); err != nil {
		ctx.CsmLogger().Errorf("绑定请求参数失败: %v", err)
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 验证请求参数
	if err := createIPRestrictionReq.Validate(); err != nil {
		ctx.CsmLogger().Errorf("参数验证失败: %v", err)
		return csmErr.NewInvalidParameterValueException(err.Error())
	}

	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)
	ctx.CsmLogger().Infof("在命名空间 %s 中为网关实例 %s 创建IP黑白名单", namespace, instanceId)

	// 生成插件名称
	pluginName := fmt.Sprintf("ip-restriction-%s", createIPRestrictionReq.Type)

	// 准备模板数据
	templateData := meta.IPRestrictionTemplateData{
		PluginName:      pluginName,
		Namespace:       namespace,
		RuleName:        createIPRestrictionReq.Name,
		RuleDescription: createIPRestrictionReq.Description,
		Type:            createIPRestrictionReq.Type,
		Scope:           createIPRestrictionReq.Scope,
		IPAddresses:     createIPRestrictionReq.IPAddresses,
		IPSourceType:    constants.IPSourceType,
		Enabled:         createIPRestrictionReq.Enabled,
		PluginURL:       constants.GetIpRestrictionPluginURL(),
	}

	// 根据类型设置状态码和消息
	if createIPRestrictionReq.Type == "blacklist" {
		templateData.Status = 403
		templateData.Message = "Your IP address is blocked By AI Gateway"
	} else {
		templateData.Status = 451
		templateData.Message = "Your IP address is not in whitelist By AI Gateway"
	}

	ctx.CsmLogger().Infof("模板数据准备完成，类型: %s，IP地址数量: %d", templateData.Type, len(templateData.IPAddresses))

	// 读取模板文件
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("获取当前目录失败: %v", err)
		return errors.Wrap(err, "failed to get current directory")
	}

	templatePath := path.Join(pwd, constants.GetIpRestrictionTemplatePath())
	templateContent, err := os.ReadFile(templatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("读取模板文件失败: %v", err)
		return errors.Wrap(err, "failed to read IP restriction template file")
	}

	// 渲染模板
	tmpl, err := template.New("ip-restriction").Parse(string(templateContent))
	if err != nil {
		ctx.CsmLogger().Errorf("解析模板失败: %v", err)
		return errors.Wrap(err, "failed to parse template")
	}

	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, templateData); err != nil {
		ctx.CsmLogger().Errorf("渲染模板失败: %v", err)
		return errors.Wrap(err, "failed to render template")
	}

	ctx.CsmLogger().Infof("模板渲染完成，开始创建WasmPlugin资源")

	// 创建资源
	yamlObjects := []string{rendered.String()}
	err = kube.CreateResources(ctx, hostingClient, yamlObjects)
	if err != nil {
		ctx.CsmLogger().Errorf("创建IP黑白名单资源失败: %v", err)
		return errors.Wrap(err, "failed to create IP restriction resources")
	}

	ctx.CsmLogger().Infof("成功创建IP黑白名单插件: %s", pluginName)

	// 构造响应数据
	currentTime := time.Now().In(time.FixedZone("CST", 8*60*60))
	createTime := currentTime.Format("2006-01-02 15:04:05")

	response := meta.CreateIPRestrictionResponse{
		ID:          pluginName,
		Enabled:     createIPRestrictionReq.Enabled,
		Name:        createIPRestrictionReq.Name,
		Description: createIPRestrictionReq.Description,
		Type:        createIPRestrictionReq.Type,
		Scope:       createIPRestrictionReq.Scope,
		IPAddresses: createIPRestrictionReq.IPAddresses,
		CreateTime:  createTime,
	}

	ctx.CsmLogger().Infof("IP黑白名单创建完成，返回响应数据")

	// 返回成功响应
	return ctx.JSON(http.StatusOK, response)
}

// checkExistingAiboxInstance 检查是否已存在相同 clusterId 且 srcProduct 为 aibox 的实例
func (core *APIServerCore) checkExistingAiboxInstance(ctx csmContext.CsmContext, accountId, region, clusterId string) ([]*meta.AIGatewayInstanceModel, error) {
	// 构造查询参数
	mrp := meta.NewCsmMeshRequestParams()
	mrp.AccountID = accountId
	mrp.Region = region

	// 获取所有网关列表
	gatewayList, err := core.aigatewayModel.GetAIGatewayList(ctx, mrp)
	if err != nil {
		return nil, err
	}

	var existingAiboxGateways []*meta.AIGatewayInstanceModel
	if gatewayList != nil {
		for _, gateway := range *gatewayList {
			// 检查是否是 aibox 产品且关联了指定的 clusterId
			if gateway.SrcProduct == constants.AIGatewayProductAibox &&
				gateway.AddedClusterID == clusterId &&
				gateway.Deleted != nil && *gateway.Deleted == 0 {
				existingAiboxGateways = append(existingAiboxGateways, gateway)
			}
		}
	}

	return existingAiboxGateways, nil
}

// CreateExtAuth 创建外部认证插件
func (core *APIServerCore) CreateExtAuth(ctx csmContext.CsmContext) error {
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取实例ID
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}
	ctx.CsmLogger().Infof("开始创建外部认证插件，实例ID: %s", instanceId)

	// 绑定请求参数
	createExtAuthReq := &meta.CreateExtAuthRequest{}
	if err := ctx.Bind(createExtAuthReq); err != nil {
		ctx.CsmLogger().Errorf("绑定请求参数失败: %v", err)
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 验证请求参数
	if err := createExtAuthReq.Validate(); err != nil {
		ctx.CsmLogger().Errorf("参数验证失败: %v", err)
		return csmErr.NewInvalidParameterValueException(err.Error())
	}

	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)
	ctx.CsmLogger().Infof("在命名空间 %s 中为网关实例 %s 创建外部认证插件", namespace, instanceId)

	// 使用用户传入的名称作为插件名称
	pluginName := createExtAuthReq.Name

	// 解析匹配规则（仅路由维度需要）
	var matchList []meta.ExtAuthMatchRule
	if createExtAuthReq.Scope == "route" {
		matchList, err = core.extractMatchListFromRoutes(
			ctx, createExtAuthReq.RouteList, namespace, hostingClient)
		if err != nil {
			ctx.CsmLogger().Errorf("解析路由匹配规则失败: %v", err)
			return errors.Wrap(err, "解析路由匹配规则失败")
		}
		ctx.CsmLogger().Infof("解析到 %d 个匹配规则", len(matchList))
	}

	// 使用用户传入的HTTP服务配置YAML，添加http_service根键
	httpServiceYAML := addHTTPServiceRoot(createExtAuthReq.HTTPService)

	// 准备模板数据
	templateData := meta.ExtAuthTemplateData{
		PluginName:      pluginName,
		Namespace:       namespace,
		RuleName:        createExtAuthReq.Name,
		RuleDescription: createExtAuthReq.Description,
		Scope:           createExtAuthReq.Scope,
		MatchType:       createExtAuthReq.MatchType,
		RouteList:       createExtAuthReq.RouteList,
		MatchList:       matchList,
		HTTPService:     httpServiceYAML,
		StatusOnError:   createExtAuthReq.StatusOnError,
		PluginURL:       constants.GetExtAuthPluginURL(),
		Enabled:         createExtAuthReq.Enabled,
	}

	ctx.CsmLogger().Infof("模板数据准备完成，生效粒度: %s", templateData.Scope)

	// 模板渲染和资源创建
	err = core.renderAndCreateExtAuthPlugin(ctx, templateData, hostingClient)
	if err != nil {
		return err
	}

	ctx.CsmLogger().Infof("成功创建外部认证插件: %s", pluginName)

	// 构造响应数据
	currentTime := time.Now().In(time.FixedZone("CST", 8*60*60))
	createTime := currentTime.Format("2006-01-02 15:04:05")

	response := meta.CreateExtAuthResponse{
		ID:            pluginName,
		Enabled:       createExtAuthReq.Enabled,
		Name:          createExtAuthReq.Name,
		Description:   createExtAuthReq.Description,
		Scope:         createExtAuthReq.Scope,
		MatchType:     createExtAuthReq.MatchType,
		RouteList:     createExtAuthReq.RouteList,
		HTTPService:   createExtAuthReq.HTTPService,
		StatusOnError: createExtAuthReq.StatusOnError,
		CreateTime:    createTime,
	}

	ctx.CsmLogger().Infof("外部认证插件创建完成，返回响应数据")

	// 返回成功响应
	return ctx.JSON(http.StatusOK, response)
}

// extractMatchListFromRoutes 从路由列表中提取匹配规则
func (core *APIServerCore) extractMatchListFromRoutes(
	ctx csmContext.CsmContext,
	routeList []string,
	namespace string,
	client kube.Client,
) ([]meta.ExtAuthMatchRule, error) {
	var matchList []meta.ExtAuthMatchRule

	istioClient := client.Istio()

	for _, routeName := range routeList {
		// 获取VirtualService资源
		vs, err := istioClient.NetworkingV1alpha3().VirtualServices(namespace).Get(
			context.TODO(), routeName, metav1.GetOptions{})
		if err != nil {
			if k8serrors.IsNotFound(err) {
				ctx.CsmLogger().Warnf("路由 %s 不存在，跳过", routeName)
				continue
			}
			return nil, errors.Wrapf(err, "获取路由 %s 失败", routeName)
		}

		// 解析HTTP路由规则
		rules := core.extractMatchRulesFromVirtualService(vs)
		matchList = append(matchList, rules...)
	}

	return matchList, nil
}

// extractMatchRulesFromVirtualService 从VirtualService中提取匹配规则
func (core *APIServerCore) extractMatchRulesFromVirtualService(vs *v1alpha3.VirtualService) []meta.ExtAuthMatchRule {
	var matchList []meta.ExtAuthMatchRule

	if vs.Spec.Http == nil {
		return matchList
	}

	for _, httpRoute := range vs.Spec.Http {
		if httpRoute.Match == nil {
			continue
		}

		for _, match := range httpRoute.Match {
			if match.Uri == nil {
				continue
			}

			var rule meta.ExtAuthMatchRule

			// 提取路径匹配规则
			if match.Uri.GetExact() != "" {
				rule.Path = match.Uri.GetExact()
				rule.Type = "exact"
			} else if match.Uri.GetPrefix() != "" {
				rule.Path = match.Uri.GetPrefix()
				rule.Type = "prefix"
			} else {
				continue // 跳过不支持的匹配类型
			}

			matchList = append(matchList, rule)
		}
	}

	return matchList
}

// addHTTPServiceRoot 为HTTP服务配置YAML添加http_service根键
func addHTTPServiceRoot(httpServiceYAML string) string {
	if httpServiceYAML == "" {
		return ""
	}

	// 为每行添加2个空格的缩进，然后添加http_service根键
	lines := strings.Split(httpServiceYAML, "\n")
	indentedLines := make([]string, 0, len(lines)+1)
	indentedLines = append(indentedLines, "http_service:")

	for _, line := range lines {
		if line != "" {
			indentedLines = append(indentedLines, "  "+line)
		} else {
			indentedLines = append(indentedLines, line)
		}
	}

	return strings.Join(indentedLines, "\n")
}

// reconstructHTTPServiceYAML 从配置中重构HTTP服务配置YAML（不包含http_service根键）
func reconstructHTTPServiceYAML(defaultConfig map[string]interface{}) string {
	httpServiceRaw, found, _ := unstructured.NestedMap(defaultConfig, "http_service")
	if !found {
		return ""
	}

	endpointMode, _ := httpServiceRaw["endpoint_mode"].(string)
	timeout, _ := httpServiceRaw["timeout"].(int64)

	yamlBuilder := strings.Builder{}
	yamlBuilder.WriteString(fmt.Sprintf("endpoint_mode: %s\n", endpointMode))
	yamlBuilder.WriteString("endpoint:\n")

	if endpointRaw, found, _ := unstructured.NestedMap(httpServiceRaw, "endpoint"); found {
		serviceName, _ := endpointRaw["service_name"].(string)
		servicePort, _ := endpointRaw["service_port"].(int64)
		pathPrefix, _ := endpointRaw["path_prefix"].(string)

		yamlBuilder.WriteString(fmt.Sprintf("  service_name: %s\n", serviceName))
		yamlBuilder.WriteString(fmt.Sprintf("  service_port: %d\n", servicePort))
		if pathPrefix != "" {
			yamlBuilder.WriteString(fmt.Sprintf("  path_prefix: %s\n", pathPrefix))
		}
	}

	yamlBuilder.WriteString(fmt.Sprintf("timeout: %d", timeout))
	return yamlBuilder.String()
}

// renderAndCreateExtAuthPlugin 渲染模板并创建外部认证插件
func (core *APIServerCore) renderAndCreateExtAuthPlugin(
	ctx csmContext.CsmContext,
	templateData meta.ExtAuthTemplateData,
	hostingClient kube.Client,
) error {
	// 读取模板文件
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("获取当前目录失败: %v", err)
		return errors.Wrap(err, "failed to get current directory")
	}

	templatePath := path.Join(pwd, constants.GetExtAuthTemplatePath())
	templateContent, err := os.ReadFile(templatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("读取模板文件失败: %v", err)
		return errors.Wrap(err, "failed to read ext-auth template file")
	}

	// 渲染模板，添加自定义函数
	tmpl, err := template.New(constants.GetExtAuthPluginName()).Funcs(template.FuncMap{
		"indent": func(spaces int, text string) string {
			if text == "" {
				return ""
			}

			lines := strings.Split(text, "\n")
			indentStr := strings.Repeat(" ", spaces)
			for i, line := range lines {
				if line != "" {
					lines[i] = indentStr + line
				}
			}
			return strings.Join(lines, "\n")
		},
	}).Parse(string(templateContent))
	if err != nil {
		ctx.CsmLogger().Errorf("解析模板失败: %v", err)
		return errors.Wrap(err, "failed to parse template")
	}

	var rendered bytes.Buffer
	if err := tmpl.Execute(&rendered, templateData); err != nil {
		ctx.CsmLogger().Errorf("渲染模板失败: %v", err)
		return errors.Wrap(err, "failed to render template")
	}

	ctx.CsmLogger().Infof("模板渲染完成，开始创建WasmPlugin资源")

	// 创建资源
	yamlObjects := []string{rendered.String()}
	err = kube.CreateOrUpdateK8sResource(ctx, hostingClient, yamlObjects)
	if err != nil {
		ctx.CsmLogger().Errorf("创建外部认证资源失败: %v", err)
		return errors.Wrap(err, "failed to create ext-auth resources")
	}

	return nil
}

// renderAndUpdateExtAuthPlugin 渲染模板并更新外部认证插件
func (core *APIServerCore) renderAndUpdateExtAuthPlugin(
	ctx csmContext.CsmContext,
	templateData meta.ExtAuthTemplateData,
	hostingClient kube.Client,
	pluginName string,
) error {
	// 读取模板文件
	pwd, err := os.Getwd()
	if err != nil {
		ctx.CsmLogger().Errorf("获取当前目录失败: %v", err)
		return errors.Wrap(err, "failed to get current directory")
	}

	templatePath := path.Join(pwd, constants.GetExtAuthTemplatePath())
	templateContent, err := os.ReadFile(templatePath)
	if err != nil {
		ctx.CsmLogger().Errorf("读取模板文件失败: %v", err)
		return errors.Wrap(err, "failed to read ext-auth template file")
	}

	// 渲染模板，添加自定义函数
	tmpl, err := template.New(constants.GetExtAuthPluginName()).Funcs(template.FuncMap{
		"indent": func(spaces int, text string) string {
			if text == "" {
				return ""
			}

			lines := strings.Split(text, "\n")
			indentStr := strings.Repeat(" ", spaces)
			for i, line := range lines {
				if line != "" {
					lines[i] = indentStr + line
				}
			}
			return strings.Join(lines, "\n")
		},
	}).Parse(string(templateContent))
	if err != nil {
		ctx.CsmLogger().Errorf("解析模板失败: %v", err)
		return errors.Wrap(err, "failed to parse template")
	}

	var rendered strings.Builder
	err = tmpl.Execute(&rendered, templateData)
	if err != nil {
		ctx.CsmLogger().Errorf("渲染模板失败: %v", err)
		return errors.Wrap(err, "failed to execute template")
	}

	ctx.CsmLogger().Infof("模板渲染完成，开始更新WasmPlugin资源")

	// 先获取现有资源以获取resourceVersion
	dynamicClient := hostingClient.Dynamic()
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	existingPlugin, err := dynamicClient.Resource(groupVersionResource).
		Namespace(templateData.Namespace).
		Get(context.TODO(), pluginName, metav1.GetOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("获取现有插件失败: %v", err)
		return errors.Wrap(err, "failed to get existing plugin")
	}

	// 解析渲染后的YAML
	decoder := yaml.NewYAMLOrJSONDecoder(strings.NewReader(rendered.String()), 4096)
	var newPlugin unstructured.Unstructured
	if err := decoder.Decode(&newPlugin); err != nil {
		ctx.CsmLogger().Errorf("解析YAML失败: %v", err)
		return errors.Wrap(err, "failed to decode YAML")
	}

	// 设置resourceVersion以支持更新
	newPlugin.SetResourceVersion(existingPlugin.GetResourceVersion())

	// 更新资源
	_, err = dynamicClient.Resource(groupVersionResource).
		Namespace(templateData.Namespace).
		Update(context.TODO(), &newPlugin, metav1.UpdateOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("更新外部认证资源失败: %v", err)
		return errors.Wrap(err, "failed to update ext-auth resources")
	}

	return nil
}

// ListExtAuths 查看外部认证插件列表
func (core *APIServerCore) ListExtAuths(ctx csmContext.CsmContext) error {
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取实例ID
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	// 获取scope查询参数
	scopeFilter := ""
	if ctx.Request() != nil && ctx.Request().URL != nil {
		scopeFilter = ctx.Request().URL.Query().Get("scope")
	}

	ctx.CsmLogger().Infof("开始查询外部认证插件列表，实例ID: %s, scope过滤: %s", instanceId, scopeFilter)

	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 获取查询参数
	nameFilter := ctx.QueryParam("name")

	// 使用dynamic client获取WasmPlugin资源，使用标签选择器过滤
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	// 使用标签选择器直接筛选外部认证插件
	dynamicClient := hostingClient.Dynamic()
	unstructuredList, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector: constants.GetExtAuthPluginLabelSelector(),
	})
	if err != nil {
		ctx.CsmLogger().Errorf("查询外部认证WasmPlugins失败: %v", err)
		return err
	}

	ctx.CsmLogger().Infof("查询到 %d 个外部认证插件", len(unstructuredList.Items))

	var extAuthItems []meta.ExtAuthItem
	for _, item := range unstructuredList.Items {
		// 从annotations中获取规则信息
		annotations := item.GetAnnotations()
		if annotations == nil {
			continue
		}

		ruleName := annotations["aigw.rule.name"]
		scope := annotations["aigw.rule.scope"]
		matchType := annotations["aigw.rule.match.type"]

		// 获取路由列表（仅路由维度）
		var routeList []string
		if scope == "route" {
			if routeListStr := annotations["aigw.rule.route.list"]; routeListStr != "" {
				routeList = strings.Split(routeListStr, ",")
			}
		}

		// 如果指定了名称过滤器，则进行过滤
		if nameFilter != "" && !strings.Contains(ruleName, nameFilter) {
			continue
		}

		// 从spec中获取配置信息
		spec, found, err := unstructured.NestedMap(item.Object, "spec")
		if err != nil || !found {
			ctx.CsmLogger().Warnf("无法获取插件 %s 的spec配置", item.GetName())
			continue
		}

		// 获取defaultConfigDisable状态
		defaultConfigDisable, _, _ := unstructured.NestedBool(spec, "defaultConfigDisable")
		enabled := !defaultConfigDisable

		// 从defaultConfig中解析配置
		defaultConfig, found, err := unstructured.NestedMap(spec, "defaultConfig")
		if err != nil || !found {
			ctx.CsmLogger().Warnf("无法获取插件 %s 的defaultConfig", item.GetName())
			continue
		}

		// 重新构造HTTP服务配置YAML字符串（不包含http_service根键）
		httpServiceYAML := reconstructHTTPServiceYAML(defaultConfig)

		// 获取认证失败状态码
		statusOnError, _, _ := unstructured.NestedInt64(defaultConfig, "status_on_error")

		// 获取创建和更新时间（从metadata中获取）
		creationTimestamp := item.GetCreationTimestamp()
		createTime := creationTimestamp.In(time.FixedZone("CST", 8*60*60)).Format("2006-01-02 15:04:05")
		updateTime := createTime // 简化处理，实际可以从resourceVersion等获取

		extAuthItem := meta.ExtAuthItem{
			Name:          ruleName,
			Enabled:       enabled,
			Scope:         scope,
			MatchType:     matchType,
			RouteList:     routeList,
			HTTPService:   httpServiceYAML,
			StatusOnError: int(statusOnError),
			CreateTime:    createTime,
			UpdateTime:    updateTime,
		}

		// 根据scope参数过滤结果
		if scopeFilter == "" || scope == scopeFilter {
			extAuthItems = append(extAuthItems, extAuthItem)
		}
	}

	ctx.CsmLogger().Infof("外部认证插件列表查询完成，返回 %d 个结果", len(extAuthItems))

	// 构造标准分页响应格式
	response := map[string]interface{}{
		"orderBy":    "createTime",
		"order":      "desc",
		"pageNo":     1,
		"pageSize":   len(extAuthItems),
		"totalCount": len(extAuthItems),
		"result":     extAuthItems,
	}

	return ctx.JSON(http.StatusOK, response)
}

// ListPluginMarket 查看插件市场已安装插件列表
func (core *APIServerCore) ListPluginMarket(ctx csmContext.CsmContext) error {
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取实例ID
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	// 获取分页参数（简化处理，使用默认值）
	pageNo := 1
	pageSize := 10
	orderBy := "createTime"
	order := "desc"

	ctx.CsmLogger().Infof("查询插件市场已安装插件列表，实例ID: %s, 分页参数: pageNo=%d, pageSize=%d", instanceId, pageNo, pageSize)

	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 构造命名空间
	gatewayNamespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 查询带有插件市场标签的WasmPlugin资源
	dynamicClient := hostingClient.Dynamic()
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	// 使用标签选择器查询插件市场插件
	listOptions := metav1.ListOptions{
		LabelSelector: "aigw.plugin.tag=plugin-market",
	}

	wasmPluginList, err := dynamicClient.Resource(groupVersionResource).
		Namespace(gatewayNamespace).
		List(context.TODO(), listOptions)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			// 返回空列表
			response := meta.PluginMarketListResponse{
				OrderBy:    orderBy,
				Order:      order,
				PageNo:     pageNo,
				PageSize:   pageSize,
				TotalCount: 0,
				Result:     []meta.PluginMarketItem{},
			}
			return ctx.JSON(http.StatusOK, response)
		}
		ctx.CsmLogger().Errorf("查询WasmPlugin资源失败: %v", err)
		return csmErr.NewInvalidParameterValueException("查询插件失败")
	}

	ctx.CsmLogger().Infof("找到 %d 个插件市场插件", len(wasmPluginList.Items))

	// 统计外部认证插件的规则数量和启用状态
	ruleCount, routeEnabledCount, gatewayEnabledCount := core.countExtAuthRules(wasmPluginList.Items)

	var pluginMarketItems []meta.PluginMarketItem

	// 确定安装状态
	installStatus := "uninstalled"
	if ruleCount.Total > 0 {
		installStatus = "installed"
	}

	// 添加外部认证插件到结果中（无论是否有规则都显示）
	pluginItem := meta.PluginMarketItem{
		PluginName:          constants.GetExtAuthPluginName(),
		RuleCount:           ruleCount,
		PluginType:          "auth",
		InstallStatus:       installStatus,
		RouteEnabledCount:   routeEnabledCount,
		GatewayEnabledCount: gatewayEnabledCount,
	}
	pluginMarketItems = append(pluginMarketItems, pluginItem)

	// 应用分页
	totalCount := len(pluginMarketItems)
	startIndex := (pageNo - 1) * pageSize
	endIndex := startIndex + pageSize

	if startIndex >= totalCount {
		pluginMarketItems = []meta.PluginMarketItem{}
	} else {
		if endIndex > totalCount {
			endIndex = totalCount
		}
		pluginMarketItems = pluginMarketItems[startIndex:endIndex]
	}

	// 构造响应
	response := meta.PluginMarketListResponse{
		OrderBy:    orderBy,
		Order:      order,
		PageNo:     pageNo,
		PageSize:   pageSize,
		TotalCount: totalCount,
		Result:     pluginMarketItems,
	}

	return ctx.JSON(http.StatusOK, response)
}

// countExtAuthRules 统计外部认证插件的规则数量和启用状态
func (core *APIServerCore) countExtAuthRules(wasmPluginItems []unstructured.Unstructured) (meta.PluginMarketRuleCount, int, int) {
	ruleCount := meta.PluginMarketRuleCount{
		Total:            0,
		RouteRuleCount:   0,
		GatewayRuleCount: 0,
	}

	routeEnabledCount := 0
	gatewayEnabledCount := 0

	// 遍历所有WasmPlugin，统计外部认证插件的规则
	for _, item := range wasmPluginItems {
		// 检查是否是外部认证插件
		labels := item.GetLabels()
		if labels == nil {
			continue
		}

		pluginType, exists := labels["aigw.plugin.type"]
		if !exists || pluginType != constants.GetExtAuthPluginName() {
			continue
		}

		// 获取annotations中的scope信息
		annotations := item.GetAnnotations()
		if annotations == nil {
			continue
		}

		scope, exists := annotations["aigw.rule.scope"]
		if !exists {
			continue
		}
		spec, _, _ := unstructured.NestedMap(item.Object, "spec")

		// 检查是否启用
		defaultConfigDisable, _, _ := unstructured.NestedBool(spec, "defaultConfigDisable")
		isEnabled := !defaultConfigDisable

		// 统计规则数量
		ruleCount.Total++
		if scope == "route" {
			ruleCount.RouteRuleCount++
			if isEnabled {
				routeEnabledCount++
			}
		} else if scope == "gateway" {
			ruleCount.GatewayRuleCount++
			if isEnabled {
				gatewayEnabledCount++
			}
		}
	}

	return ruleCount, routeEnabledCount, gatewayEnabledCount
}

// ListRoutesWithExtAuth 查询路由列表（包含外部认证关联状态）
func (core *APIServerCore) ListRoutesWithExtAuth(ctx csmContext.CsmContext) error {
	region := ctx.Get(reg.ContextRegion).(string)
	instanceId := ctx.Param("instanceId")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}

	ctx.CsmLogger().Infof("开始查询路由列表，实例ID: %s", instanceId)

	// 获取分页参数
	pageNo := 1
	pageSize := 10
	orderBy := "routeName"
	order := "asc"

	if pageNoStr := ctx.QueryParam("pageNo"); pageNoStr != "" {
		if p, err := strconv.Atoi(pageNoStr); err == nil && p > 0 {
			pageNo = p
		}
	}
	if pageSizeStr := ctx.QueryParam("pageSize"); pageSizeStr != "" {
		if p, err := strconv.Atoi(pageSizeStr); err == nil && p > 0 {
			pageSize = p
		}
	}
	if orderByParam := ctx.QueryParam("orderBy"); orderByParam != "" {
		orderBy = orderByParam
	}
	if orderParam := ctx.QueryParam("order"); orderParam != "" {
		order = orderParam
	}

	// 获取网关命名空间
	gatewayNamespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return csmErr.NewDBOperationException(err)
	}

	dynamicClient := hostingClient.Dynamic()

	// 性能优化：批量查询所有资源
	// 1. 查询所有VirtualService（路由）
	istioClient := hostingClient.Istio()
	vsList, err := istioClient.NetworkingV1alpha3().VirtualServices(gatewayNamespace).List(context.TODO(), metav1.ListOptions{})

	// 2. 查询所有WasmPlugin（用于检查外部认证关联）
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	wasmPluginList, err := dynamicClient.Resource(groupVersionResource).
		Namespace(gatewayNamespace).
		List(context.TODO(), metav1.ListOptions{
			LabelSelector: "aigw.plugin.type=ext-auth,aigw.plugin.tag=plugin-market",
		})
	if err != nil {
		ctx.CsmLogger().Errorf("查询WasmPlugin失败: %v", err)
		return csmErr.NewDBOperationException(err)
	}

	ctx.CsmLogger().Infof("找到 %d 个路由，%d 个外部认证插件", len(vsList.Items), len(wasmPluginList.Items))

	// 构建路由与外部认证插件的关联映射（性能优化：一次性构建映射）
	routeExtAuthMap := make(map[string]bool)
	for _, plugin := range wasmPluginList.Items {
		annotations := plugin.GetAnnotations()
		if annotations == nil {
			continue
		}

		// 检查scope，只处理路由维度的插件
		scope, exists := annotations["aigw.rule.scope"]
		if !exists || scope != "route" {
			continue
		}

		// 获取关联的路由列表
		routeListStr, exists := annotations["aigw.rule.route.list"]
		if !exists || routeListStr == "" {
			continue
		}

		// 解析路由列表
		routeNames := strings.Split(routeListStr, ",")
		for _, routeName := range routeNames {
			routeName = strings.TrimSpace(routeName)
			if routeName != "" {
				routeExtAuthMap[routeName] = true
			}
		}
	}

	// 构建路由列表
	var routeItems []meta.RouteItem
	for _, vs := range vsList.Items {
		routeName := vs.GetName()
		hasExtAuth := routeExtAuthMap[routeName]

		routeItem := meta.RouteItem{
			RouteName:  routeName,
			HasExtAuth: hasExtAuth,
		}
		routeItems = append(routeItems, routeItem)
	}

	ctx.CsmLogger().Infof("构建了 %d 个路由项", len(routeItems))

	// 排序
	sort.Slice(routeItems, func(i, j int) bool {
		switch orderBy {
		case "routeName":
			if order == "desc" {
				return routeItems[i].RouteName > routeItems[j].RouteName
			}
			return routeItems[i].RouteName < routeItems[j].RouteName
		case "hasExtAuth":
			if order == "desc" {
				return routeItems[i].HasExtAuth && !routeItems[j].HasExtAuth
			}
			return !routeItems[i].HasExtAuth && routeItems[j].HasExtAuth
		default:
			return routeItems[i].RouteName < routeItems[j].RouteName
		}
	})

	// 应用分页
	totalCount := len(routeItems)
	startIndex := (pageNo - 1) * pageSize
	endIndex := startIndex + pageSize

	if startIndex >= totalCount {
		routeItems = []meta.RouteItem{}
	} else {
		if endIndex > totalCount {
			endIndex = totalCount
		}
		routeItems = routeItems[startIndex:endIndex]
	}

	// 构造响应
	response := meta.RouteListResponse{
		OrderBy:    orderBy,
		Order:      order,
		PageNo:     pageNo,
		PageSize:   pageSize,
		TotalCount: totalCount,
		Result:     routeItems,
	}

	ctx.CsmLogger().Infof("路由列表查询完成，返回 %d 个路由", len(routeItems))

	return ctx.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"status":  200,
		"page":    response,
	})
}

// GetExtAuthDetail 查看外部认证插件详情
func (core *APIServerCore) GetExtAuthDetail(ctx csmContext.CsmContext) error {
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取实例ID和插件ID
	instanceId := ctx.Param("instanceId")
	ruleName := ctx.Param("ruleName")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}
	if ruleName == "" {
		return csmErr.NewMissingParametersException("ruleName")
	}
	ctx.CsmLogger().Infof("开始查询外部认证插件详情，实例ID: %s，插件ID: %s", instanceId, ruleName)

	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 使用dynamic client获取WasmPlugin资源
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := hostingClient.Dynamic()
	plugin, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).Get(context.TODO(), ruleName, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return csmErr.NewResourceNotFoundException("外部认证插件不存在", err)
		}
		ctx.CsmLogger().Errorf("查询WasmPlugin失败: %v", err)
		return err
	}

	// 验证是否为外部认证插件
	labels := plugin.GetLabels()
	if labels == nil || labels[constants.WasmPluginLabelPluginType] != constants.WasmPluginTypeExtAuth {
		return csmErr.NewResourceNotFoundException("指定的资源不是外部认证插件", nil)
	}

	// 从annotations中获取规则信息
	annotations := plugin.GetAnnotations()
	if annotations == nil {
		return csmErr.NewInvalidParameterValueException("插件缺少必要的注解信息")
	}

	ruleDescription := annotations["aigw.rule.description"]
	scope := annotations["aigw.rule.scope"]
	matchType := annotations["aigw.rule.match.type"]

	// 获取路由列表（仅路由维度）
	var routeList []string
	if scope == "route" {
		if routeListStr := annotations["aigw.rule.route.list"]; routeListStr != "" {
			routeList = strings.Split(routeListStr, ",")
		}
	}

	// 从spec中获取配置信息
	spec, found, err := unstructured.NestedMap(plugin.Object, "spec")
	if err != nil || !found {
		return csmErr.NewInvalidParameterValueException("无法获取插件配置信息")
	}

	// 获取defaultConfigDisable状态
	defaultConfigDisable, _, _ := unstructured.NestedBool(spec, "defaultConfigDisable")
	enabled := !defaultConfigDisable

	// 从defaultConfig中解析配置
	defaultConfig, found, err := unstructured.NestedMap(spec, "defaultConfig")
	if err != nil || !found {
		return csmErr.NewInvalidParameterValueException("无法获取插件默认配置")
	}

	// 解析匹配规则列表（仅路由维度）
	var matchList []meta.ExtAuthMatchRule
	if scope == "route" {
		if matchListRaw, found, _ := unstructured.NestedSlice(defaultConfig, "match_list"); found {
			for _, matchRaw := range matchListRaw {
				if matchMap, ok := matchRaw.(map[string]interface{}); ok {
					path, _ := matchMap["match_rule_path"].(string)
					ruleType, _ := matchMap["match_rule_type"].(string)
					if path != "" && ruleType != "" {
						matchList = append(matchList, meta.ExtAuthMatchRule{
							Path: path,
							Type: ruleType,
						})
					}
				}
			}
		}
	}

	// 重新构造HTTP服务配置YAML字符串（不包含http_service根键）
	httpServiceYAML := reconstructHTTPServiceYAML(defaultConfig)

	// 获取认证失败状态码
	statusOnError, _, _ := unstructured.NestedInt64(defaultConfig, "status_on_error")

	// 获取创建和更新时间
	creationTimestamp := plugin.GetCreationTimestamp()
	createTime := creationTimestamp.In(time.FixedZone("CST", 8*60*60)).Format("2006-01-02 15:04:05")
	updateTime := createTime // 简化处理

	// 构造响应数据
	response := meta.ExtAuthInfo{
		Enabled:       enabled,
		Name:          ruleName,
		Description:   ruleDescription,
		Scope:         scope,
		MatchType:     matchType,
		RouteList:     routeList,
		MatchList:     matchList,
		HTTPService:   httpServiceYAML,
		StatusOnError: int(statusOnError),
		CreateTime:    createTime,
		UpdateTime:    updateTime,
	}

	ctx.CsmLogger().Infof("外部认证插件详情查询完成")

	// 返回成功响应
	return ctx.JSON(http.StatusOK, response)
}

// UpdateExtAuth 编辑外部认证插件
func (core *APIServerCore) UpdateExtAuth(ctx csmContext.CsmContext) error {
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取实例ID和插件ID
	instanceId := ctx.Param("instanceId")
	ruleName := ctx.Param("ruleName")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}
	if ruleName == "" {
		return csmErr.NewMissingParametersException("ruleName")
	}
	ctx.CsmLogger().Infof("开始编辑外部认证插件，实例ID: %s，插件ID: %s", instanceId, ruleName)

	// 绑定请求参数
	updateExtAuthReq := &meta.UpdateExtAuthRequest{}
	if err := ctx.Bind(updateExtAuthReq); err != nil {
		ctx.CsmLogger().Errorf("绑定请求参数失败: %v", err)
		return csmErr.NewInvalidParameterValueExceptionWithoutMsg(err)
	}

	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 解析匹配规则（仅路由维度需要）
	var matchList []meta.ExtAuthMatchRule
	if updateExtAuthReq.Scope == "route" {
		matchList, err = core.extractMatchListFromRoutes(
			ctx, updateExtAuthReq.RouteList, namespace, hostingClient)
		if err != nil {
			ctx.CsmLogger().Errorf("解析路由匹配规则失败: %v", err)
			return errors.Wrap(err, "解析路由匹配规则失败")
		}
		ctx.CsmLogger().Infof("解析到 %d 个匹配规则", len(matchList))
	}

	// 使用用户传入的HTTP服务配置YAML，添加http_service根键
	httpServiceYAML := addHTTPServiceRoot(updateExtAuthReq.HTTPService)

	// 准备更新的模板数据
	templateData := meta.ExtAuthTemplateData{
		PluginName:      ruleName, // 保持插件名称不变
		Namespace:       namespace,
		RuleName:        ruleName,
		RuleDescription: updateExtAuthReq.Description,
		Scope:           updateExtAuthReq.Scope,
		MatchType:       updateExtAuthReq.MatchType,
		RouteList:       updateExtAuthReq.RouteList,
		MatchList:       matchList,
		HTTPService:     httpServiceYAML,
		StatusOnError:   updateExtAuthReq.StatusOnError,
		PluginURL:       constants.GetExtAuthPluginURL(),
		Enabled:         updateExtAuthReq.Enabled,
	}

	ctx.CsmLogger().Infof("模板数据准备完成，生效粒度: %s", templateData.Scope)

	err = core.renderAndUpdateExtAuthPlugin(ctx, templateData, hostingClient, ruleName)
	if err != nil {
		return err
	}

	ctx.CsmLogger().Infof("成功更新外部认证插件: %s", ruleName)

	// 获取更新时间
	currentTime := time.Now().In(time.FixedZone("CST", 8*60*60))
	updateTime := currentTime.Format("2006-01-02 15:04:05")

	response := meta.ExtAuthInfo{
		Enabled:       updateExtAuthReq.Enabled,
		Name:          ruleName,
		Description:   updateExtAuthReq.Description,
		Scope:         updateExtAuthReq.Scope,
		MatchType:     updateExtAuthReq.MatchType,
		RouteList:     updateExtAuthReq.RouteList,
		HTTPService:   updateExtAuthReq.HTTPService,
		StatusOnError: updateExtAuthReq.StatusOnError,
		UpdateTime:    updateTime,
	}

	ctx.CsmLogger().Infof("外部认证插件编辑完成，返回响应数据")

	// 返回成功响应
	return ctx.JSON(http.StatusOK, response)
}

// DeleteExtAuth 删除外部认证插件
func (core *APIServerCore) DeleteExtAuth(ctx csmContext.CsmContext) error {
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取实例ID和插件ID
	instanceId := ctx.Param("instanceId")
	ruleName := ctx.Param("ruleName")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}
	if ruleName == "" {
		return csmErr.NewMissingParametersException("ruleName")
	}
	ctx.CsmLogger().Infof("开始删除外部认证插件，实例ID: %s，插件ID: %s", instanceId, ruleName)

	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 使用固定的命名空间
	namespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 使用dynamic client删除WasmPlugin资源
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := hostingClient.Dynamic()

	// 先检查资源是否存在
	plugin, err := dynamicClient.Resource(groupVersionResource).Namespace(namespace).Get(context.TODO(), ruleName, metav1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return csmErr.NewResourceNotFoundException("外部认证插件不存在", err)
		}
		ctx.CsmLogger().Errorf("查询WasmPlugin失败: %v", err)
		return err
	}

	// 验证是否为外部认证插件
	labels := plugin.GetLabels()
	if labels == nil || labels[constants.WasmPluginLabelPluginType] != constants.WasmPluginTypeExtAuth {
		return csmErr.NewResourceNotFoundException("指定的资源不是外部认证插件", nil)
	}

	// 执行删除操作
	err = dynamicClient.Resource(groupVersionResource).Namespace(namespace).Delete(context.TODO(), ruleName, metav1.DeleteOptions{})
	if err != nil {
		ctx.CsmLogger().Errorf("删除WasmPlugin失败: %v", err)
		return err
	}

	ctx.CsmLogger().Infof("成功删除外部认证插件: %s", ruleName)

	// 构造响应数据
	response := meta.DeleteExtAuthResponse{
		Success: true,
		Status:  http.StatusOK,
		Message: "外部认证插件删除成功",
	}

	// 返回成功响应
	return ctx.JSON(http.StatusOK, response)
}

// UninstallPlugin 卸载插件（全量删除规则）
func (core *APIServerCore) UninstallPlugin(ctx csmContext.CsmContext) error {
	region := ctx.Get(reg.ContextRegion).(string)
	// 获取实例ID和插件名称
	instanceId := ctx.Param("instanceId")
	pluginName := ctx.Param("pluginName")
	if instanceId == "" {
		return csmErr.NewMissingParametersException("instanceId")
	}
	if pluginName == "" {
		return csmErr.NewMissingParametersException("pluginName")
	}

	ctx.CsmLogger().Infof("开始卸载插件，实例ID: %s，插件名称: %s", instanceId, pluginName)

	// 验证插件名称（目前仅支持ext-auth）
	if pluginName != "ext-auth" {
		return csmErr.NewInvalidParameterValueException("不支持的插件名称，目前仅支持: ext-auth")
	}

	// 创建k8s客户端
	hostedClusterId, _ := core.InstancesService.GetHostingCluster(ctx, region)
	if len(hostedClusterId) == 0 {
		return csmErr.NewInvalidParameterValueException("no hosting cluster found")
	}

	hostingClient, err := core.cceService.NewClient(ctx, region, hostedClusterId, meta.HostingMeshType)
	if err != nil {
		ctx.CsmLogger().Errorf("创建集群客户端失败: %v", err)
		return err
	}

	// 构造命名空间
	gatewayNamespace := fmt.Sprintf("istio-system-%s", instanceId)

	// 使用dynamic client批量查询和删除WasmPlugin资源
	groupVersionResource := schema.GroupVersionResource{
		Group:    "extensions.higress.io",
		Version:  "v1alpha1",
		Resource: "wasmplugins",
	}

	dynamicClient := hostingClient.Dynamic()

	// 性能优化：使用标签选择器批量查询所有相关插件
	var labelSelector string
	switch pluginName {
	case "ext-auth":
		labelSelector = constants.GetExtAuthPluginLabelSelector()
	default:
		return csmErr.NewInvalidParameterValueException("不支持的插件类型")
	}

	ctx.CsmLogger().Infof("使用标签选择器查询插件: %s", labelSelector)

	// 批量查询所有相关的WasmPlugin资源
	wasmPluginList, err := dynamicClient.Resource(groupVersionResource).
		Namespace(gatewayNamespace).
		List(context.TODO(), metav1.ListOptions{
			LabelSelector: labelSelector,
		})
	if err != nil {
		ctx.CsmLogger().Errorf("查询WasmPlugin资源失败: %v", err)
		return csmErr.NewInvalidParameterValueException("查询插件失败")
	}

	pluginCount := len(wasmPluginList.Items)
	ctx.CsmLogger().Infof("找到 %d 个需要删除的插件实例", pluginCount)

	if pluginCount == 0 {
		return csmErr.NewResourceNotFoundException("未找到需要卸载的插件", nil)
	}

	// 性能优化：并发删除所有插件实例
	deletedCount := 0
	errorCount := 0

	// 使用channel来收集删除结果
	type deleteResult struct {
		name    string
		success bool
		err     error
	}

	resultChan := make(chan deleteResult, pluginCount)

	// 并发删除插件
	for _, plugin := range wasmPluginList.Items {
		go func(pluginName string) {
			err := dynamicClient.Resource(groupVersionResource).
				Namespace(gatewayNamespace).
				Delete(context.TODO(), pluginName, metav1.DeleteOptions{})

			resultChan <- deleteResult{
				name:    pluginName,
				success: err == nil,
				err:     err,
			}
		}(plugin.GetName())
	}

	// 收集删除结果
	for i := 0; i < pluginCount; i++ {
		result := <-resultChan
		if result.success {
			deletedCount++
			ctx.CsmLogger().Infof("成功删除插件实例: %s", result.name)
		} else {
			errorCount++
			ctx.CsmLogger().Errorf("删除插件实例失败: %s, 错误: %v", result.name, result.err)
		}
	}

	ctx.CsmLogger().Infof("插件卸载完成，成功删除: %d，失败: %d", deletedCount, errorCount)

	// 如果有部分失败，返回部分成功的信息
	var message string
	if errorCount > 0 {
		message = fmt.Sprintf("插件卸载部分成功，成功删除 %d 个规则，失败 %d 个", deletedCount, errorCount)
	} else {
		message = fmt.Sprintf("插件卸载成功，共删除 %d 个规则", deletedCount)
	}

	// 构造响应数据
	uninstallTime := time.Now().In(time.FixedZone("CST", 8*60*60)).Format("2006-01-02 15:04:05")
	response := meta.UninstallPluginResponse{
		Message:       message,
		PluginName:    pluginName,
		DeletedCount:  deletedCount,
		UninstallTime: uninstallTime,
	}

	return ctx.JSON(http.StatusOK, response)
}
