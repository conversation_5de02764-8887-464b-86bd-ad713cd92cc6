package router

import (
	"github.com/labstack/echo/v4"

	"icode.baidu.com/baidu/bce-api/api-logic-csm/cmd/csm/app/core"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server"
	"icode.baidu.com/baidu/bce-api/api-logic-csm/pkg/server/middleware"
)

type AIGatewayRouter struct{}

func (gatewayRouter *AIGatewayRouter) RegisterAIGWOpenAPI(c *core.APIServerCore, group *echo.Group) *echo.Group {
	// Add new endpoint for AI Gateway
	group.POST("/v1/aigateway", server.CsmHandler(c.CreateAIGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/v1/aigateway/:InstanceId", server.CsmHandler(c.GetAIGatewayDetail),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.ResolveInstancesResource(c.InstancesModel),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadInstance),
	)

	group.GET("/v1/aigateway/list", server.CsmHandler(c.GetAllIngressInstances),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		//middleware.CheckIAMDefaultDeny(c.ServiceName, permission.ReadInstance),
	)
	// Add new endpoint for deleting AI Gateway
	group.DELETE("/v1/aigateway/:InstanceId", server.CsmHandler(c.DeleteAIGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// Add new endpoint for updating AI Gateway
	group.PUT("/v1/aigateway/:InstanceId", server.CsmHandler(c.UpdateAIGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// Add new endpoint for associating clusters with AI Gateway
	group.POST("/v1/aigateway/instance/:InstanceId/clusterList", server.CsmHandler(c.AssociateClusterWithAIGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// Add new endpoint for getting AI Gateway associated clusters
	group.GET("/v1/aigateway/instance/:InstanceId/clusterList", server.CsmHandler(c.GetAIGatewayClusterList),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// Add new endpoint for removing cluster from AI Gateway
	group.DELETE("/v1/aigateway/cluster/:InstanceId/:ClusterId", server.CsmHandler(c.RemoveClusterFromAIGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// Add new endpoint for adding services to AI Gateway
	group.POST("/v1/aigateway/cluster/:instanceId/serviceList", server.CsmHandler(c.AddServiceToGateway),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// Add new endpoint for getting services list by instance ID
	group.GET("/v1/aigateway/:instanceId/service/list", server.CsmHandler(c.GetServicesByInstanceID),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/v1/aigateway/:instanceId/service", server.CsmHandler(c.GetServicesByServicesource),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// Add new endpoint for getting service details by instance ID and service name
	group.GET("/v1/aigateway/:instanceId/:serviceName/service", server.CsmHandler(c.GetServiceDetail),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// Add new endpoint for deleting service by instance ID, service name and namespace
	group.DELETE("/v1/aigateway/:instanceId/:serviceName/:namespace/service", server.CsmHandler(c.DeleteService),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/v1/aigateway/cluster/clusterList", server.CsmHandler(c.ListCceClusterByVPC),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加根据集群ID查询命名空间列表的接口
	group.GET("/v1/aigateway/cluster/namespace", server.CsmHandler(c.GetClusterNamespaces),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加根据集群ID和命名空间查询服务列表的接口
	group.GET("/v1/aigateway/cluster/service", server.CsmHandler(c.GetClusterServices),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/v1/aigateway/vpc/vpcList", server.CsmHandler(c.ListVPC),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/v1/aigateway/vpc/:vpcId/subnetList", server.CsmHandler(c.ListSubnet),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	group.GET("/v1/aigateway/vpc/:vpcId/securityGroupList", server.CsmHandler(c.ListSecurityGroup),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 消费者列表查询接口
	group.GET("/v1/aigateway/:InstanceId/consumers", server.CsmHandler(c.ListConsumers),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 创建消费者接口
	group.POST("/v1/aigateway/:InstanceId/consumer", server.CsmHandler(c.CreateConsumer),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 查询消费者详情接口
	group.GET("/v1/aigateway/:InstanceId/consumer/:ConsumerID", server.CsmHandler(c.GetConsumerDetail),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 编辑消费者接口
	group.PUT("/v1/aigateway/:InstanceId/consumer/:ConsumerID", server.CsmHandler(c.UpdateConsumer),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 删除消费者接口
	group.DELETE("/v1/aigateway/:InstanceId/consumer/:ConsumerID", server.CsmHandler(c.DeleteConsumer),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加创建路由接口
	group.POST("/v1/aigateway/:instanceId/:clusterId/route", server.CsmHandler(c.CreateRoute),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加根据集群ID、服务名称和命名空间查询服务端口信息的接口
	group.GET("/v1/aigateway/:clusterId/:serviceName/:namespace/port", server.CsmHandler(c.GetServicePortInfo),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加查询路由列表接口
	group.GET("/v1/aigateway/cluster/:instanceId/route", server.CsmHandler(c.ListRoutes),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加查询路由详情接口
	group.GET("/v1/aigateway/:instanceId/:routeName/route/detail", server.CsmHandler(c.GetRouteDetail),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加更新路由接口
	group.PUT("/v1/aigateway/:instanceId/:routeName/route/detail", server.CsmHandler(c.UpdateRoute),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加删除路由接口
	group.DELETE("/v1/aigateway/:instanceId/:routeName/route/detail", server.CsmHandler(c.DeleteRoute),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加创建IP黑白名单接口
	group.POST("/v1/aigateway/:instanceId/ipRestriction", server.CsmHandler(c.CreateIPRestriction),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加查看IP黑白名单列表接口
	group.GET("/v1/aigateway/:instanceId/ipRestrictionList", server.CsmHandler(c.ListIPRestrictions),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加查看IP黑白名单详情接口
	group.GET("/v1/aigateway/:instanceId/ipRestriction/:id", server.CsmHandler(c.GetIPRestrictionDetail),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加编辑IP黑白名单接口
	group.PUT("/v1/aigateway/:instanceId/ipRestriction/:id", server.CsmHandler(c.UpdateIPRestriction),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加删除IP黑白名单接口
	group.DELETE("/v1/aigateway/:instanceId/ipRestriction/:id", server.CsmHandler(c.DeleteIPRestriction),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加创建外部认证接口
	group.POST("/v1/aigateway/:instanceId/extAuth", server.CsmHandler(c.CreateExtAuth),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加查看外部认证列表接口
	group.GET("/v1/aigateway/:instanceId/extAuthList", server.CsmHandler(c.ListExtAuths),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加查看外部认证详情接口
	group.GET("/v1/aigateway/:instanceId/extAuth/:ruleName", server.CsmHandler(c.GetExtAuthDetail),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加编辑外部认证接口
	group.PUT("/v1/aigateway/:instanceId/extAuth/:ruleName", server.CsmHandler(c.UpdateExtAuth),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加删除外部认证接口
	group.DELETE("/v1/aigateway/:instanceId/extAuth/:ruleName", server.CsmHandler(c.DeleteExtAuth),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加插件市场安装插件查询接口
	group.GET("/v1/aigateway/:instanceId/pluginMarket", server.CsmHandler(c.ListPluginMarket),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 添加卸载插件接口
	group.DELETE("/v1/aigateway/:instanceId/pluginMarket/:pluginName", server.CsmHandler(c.UninstallPlugin),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// 查询路由列表（包含外部认证关联状态）
	group.GET("/v1/aigateway/:instanceId/routes", server.CsmHandler(c.ListRoutesWithExtAuth),
		middleware.CheckIAMSignature(),
		middleware.CheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	// JWT token 接口
	group.POST("/v1/aigateway/jwt/:instanceId/:clusterID", server.CsmHandler(c.GenerateJWTToken),
		middleware.CheckIAMSignature(),
		middleware.OpenAPICheckAndSetRegion(),
		middleware.FilterEmptyQueryParam(),
	)

	return group
}
